# Exemplo de Configuração para o Bot League of Legends
# Copie este arquivo para config.py e ajuste conforme necessário

# ============================================================================
# CONFIGURAÇÕES BÁSICAS
# ============================================================================

# Títulos das janelas para detectar o jogo
GAME_WINDOW_TITLE = "League of Legends"
CLIENT_WINDOW_TITLE = "League of Legends"
RIOT_CLIENT_TITLE = "Riot Client"

# Automação do cliente
AUTO_START_GAME = True          # Iniciar jogo automaticamente
GAME_MODE = "intro_bots"        # intro_bots, beginner_bots, intermediate_bots
PREFERRED_LANE = "bot"          # top, jungle, mid, bot, support
RANDOM_CHAMPION = True          # Selecionar campeão aleatório

# ============================================================================
# CONTROLES DO JOGO
# ============================================================================

# Teclas do jogo (ajuste conforme suas configurações)
KEY_BINDINGS = {
    'Q': 'q',
    'W': 'w',
    'E': 'e',
    'R': 'r',
    'Flash': 'd',       # Summoner spell 1
    'Teleport': 'f',    # Summoner spell 2
    'Recall': 'b',
    'Shop': 'p',
    'Move Forward': 'w',
    'Move Backward': 's',
    'Move Left': 'a',
    'Move Right': 'd'
}

# ============================================================================
# CONFIGURAÇÕES DE SAÚDE E SEGURANÇA
# ============================================================================

# Limites de vida para ações
HEALTH_LOW_THRESHOLD = 25       # Recall se vida abaixo deste valor
HEALTH_CRITICAL_THRESHOLD = 10  # Recall de emergência
HEALTH_SAFE_THRESHOLD = 60      # Considerar ações agressivas acima deste valor

# Configurações de combate
MIN_HEALTH_FOR_COMBAT = 30      # Vida mínima para lutar
MIN_ABILITIES_FOR_COMBAT = 2    # Habilidades mínimas disponíveis para lutar

# ============================================================================
# SISTEMA DE DECISÃO
# ============================================================================

# Pesos para o sistema de decisão
WEIGHTS = {
    'SAFETY': 10,       # Sobrevivência é prioridade máxima
    'COMBAT': 8,        # Lutar quando vantajoso
    'FARMING': 7,       # Crescimento econômico
    'POSITIONING': 5,   # Posicionamento no mapa
    'OBJECTIVES': 9     # Torres, dragões, baron
}

# ============================================================================
# TIMING E PERFORMANCE
# ============================================================================

# Velocidade do bot
ACTION_DELAY = 0.3              # Delay entre ações (segundos)
SCREENSHOT_INTERVAL = 0.3       # Frequência de screenshots (segundos)

# ============================================================================
# DETECÇÃO VISUAL
# ============================================================================

# Regiões da tela (x, y, largura, altura) - AJUSTE PARA SUA RESOLUÇÃO
HEALTH_BAR_REGION = (100, 50, 300, 20)     # Barra de vida do jogador
ABILITY_BAR_REGION = (400, 600, 400, 50)   # Barra de habilidades

# ============================================================================
# DETECÇÃO DE INIMIGOS POR COR VERMELHA
# ============================================================================

# Cores vermelhas em HSV (Matiz, Saturação, Valor)
# IMPORTANTE: Ajuste estes valores usando test_color_detection.py

# Primeira faixa de vermelho (0-10 graus no HSV)
RED_COLOR_LOWER = [0, 100, 100]    # Vermelho mínimo
RED_COLOR_UPPER = [10, 255, 255]   # Vermelho máximo

# Segunda faixa de vermelho (160-180 graus no HSV)
RED_COLOR_LOWER2 = [160, 100, 100] # Segundo vermelho mínimo
RED_COLOR_UPPER2 = [180, 255, 255] # Segundo vermelho máximo

# Detecção específica para barras de vida
RED_HEALTH_BAR_LOWER = [0, 150, 150]   # Mais específico para barras de vida
RED_HEALTH_BAR_UPPER = [8, 255, 255]   # Mais específico para barras de vida

# ============================================================================
# DETECÇÃO DE MINIONS ALIADOS
# ============================================================================

# Cores para minions aliados (azul)
MINION_COLOR_LOWER = [90, 50, 50]      # HSV para minions azuis
MINION_COLOR_UPPER = [130, 255, 255]   # HSV para minions azuis

# ============================================================================
# MINIMAPA E NAVEGAÇÃO
# ============================================================================

# Região do minimapa (x, y, largura, altura) - AJUSTE PARA SUA RESOLUÇÃO
MINIMAP_REGION = (1050, 650, 200, 200)

# Posições das rotas no minimapa (x, y) - AJUSTE PARA SUA RESOLUÇÃO
LANE_POSITIONS = {
    'top': (1100, 680),     # Rota superior
    'jungle': (1130, 720),  # Selva
    'mid': (1150, 750),     # Rota do meio
    'bot': (1180, 780),     # Rota inferior
    'support': (1180, 780)  # Suporte (mesma posição do ADC)
}

# ============================================================================
# CONFIGURAÇÕES AVANÇADAS
# ============================================================================

# Caminho do Tesseract OCR (opcional, para detecção de texto)
TESSERACT_PATH = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Modo debug (mostra informações detalhadas no console)
DEBUG = True

# Sistema de decisão avançado
USE_ADVANCED_DECISION_ENGINE = False  # True para usar sistema avançado

# ============================================================================
# DICAS DE CONFIGURAÇÃO
# ============================================================================

"""
RESOLUÇÃO RECOMENDADA: 1920x1080 (Full HD)

CONFIGURAÇÕES GRÁFICAS RECOMENDADAS:
- Qualidade: Média
- Sombras: Desabilitadas ou Baixas
- Efeitos: Médios
- Modo: Janela sem bordas ou Tela cheia

CALIBRAÇÃO DE CORES:
1. Execute: python test_color_detection.py
2. Escolha opção 2 (Calibrate red colors)
3. Ajuste os valores até detectar inimigos corretamente
4. Copie os valores para este arquivo

RESOLUÇÃO DE PROBLEMAS:
- Se não detectar inimigos: calibre as cores vermelhas
- Se não encontrar janela: ajuste GAME_WINDOW_TITLE
- Se cliques errados: ajuste MINIMAP_REGION e LANE_POSITIONS
- Se muito lento: diminua SCREENSHOT_INTERVAL
- Se muito rápido: aumente ACTION_DELAY

ROTAS DISPONÍVEIS:
- 'top': Rota superior (solo)
- 'jungle': Selva
- 'mid': Rota do meio (solo)
- 'bot': Rota inferior (ADC)
- 'support': Suporte (acompanha ADC)
"""
