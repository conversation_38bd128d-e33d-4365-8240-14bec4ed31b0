import pyautogui
import cv2
import numpy as np
from PIL import Image
import pytesseract
import mss
import time
import sys
import os
from vision import Vision
from actions import Actions
from decision_engine import DecisionEngine, GameState
import config

# Disclaimer: Automating League of Legends violates Riot's Terms of Service
# This bot is for educational purposes only. Use at your own risk.
print("WARNING: Automating League of Legends is against Riot's Terms of Service.")
print("This may result in permanent account bans. Proceed with caution.")

# Configure pytesseract (adjust path if needed)
pytesseract.pytesseract.tesseract_cmd = config.TESSERACT_PATH

class LoLBot:
    def __init__(self):
        self.game_window = None
        self.sct = mss.mss()
        self.vision = Vision()
        self.actions = None
        self.decision_engine = DecisionEngine()
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1

    def find_game_window(self):
        """Find the League of Legends window"""
        try:
            # This is a placeholder - need to implement window detection
            windows = pyautogui.getWindowsWithTitle(config.GAME_WINDOW_TITLE)
            if windows:
                self.game_window = windows[0]
                self.game_window.activate()
                self.actions = Actions(self.game_window)
                return True
            return False
        except Exception as e:
            print(f"Error finding game window: {e}")
            return False

    def take_screenshot(self):
        """Take screenshot of game area"""
        if self.game_window:
            bbox = (self.game_window.left, self.game_window.top,
                   self.game_window.left + self.game_window.width,
                   self.game_window.top + self.game_window.height)
            screenshot = self.sct.grab(bbox)
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            return np.array(img)
        return None

    def make_decision(self, health, minions, abilities, enemy):
        """Advanced decision making logic using weighted scoring"""
        game_state = {
            'health': health,
            'minions': minions,
            'abilities': abilities,
            'enemy': enemy,
            'timestamp': time.time()
        }

        # Calculate scores for each possible action
        action_scores = {}
        possible_actions = ['recall', 'fight_champion', 'farm', 'move_to_lane']

        for action in possible_actions:
            action_scores[action] = self.calculate_action_score(action, game_state)

        # Return the action with the highest score
        best_action = max(action_scores, key=action_scores.get)

        if config.DEBUG:
            print(f"Action scores: {action_scores}")
            print(f"Selected action: {best_action}")

        return best_action

    def calculate_action_score(self, action, game_state):
        """Calculate weighted score for each action"""
        score = 0
        health = game_state['health']
        minions = game_state['minions']
        abilities = game_state['abilities']
        enemy = game_state['enemy']

        if action == 'recall':
            # High priority if health is low
            if health < config.HEALTH_CRITICAL_THRESHOLD:
                score += 1000  # Emergency recall
            elif health < config.HEALTH_LOW_THRESHOLD:
                score += 500   # Standard recall threshold

            # Consider mana and resources (placeholder)
            # score += self.get_resource_score()

            # Penalty if enemies nearby (dangerous to recall)
            if enemy:
                score -= 300

        elif action == 'fight_champion':
            if enemy and health > config.HEALTH_LOW_THRESHOLD:
                # Base combat score
                score += 400

                # Bonus if abilities are available
                available_abilities = sum(1 for ability in ['Q', 'W', 'E', 'R']
                                        if abilities.get(ability, False))
                score += available_abilities * 50

                # Health advantage consideration
                if health > 60:
                    score += 100
                elif health < 30:
                    score -= 200

            else:
                score = 0  # Can't fight without enemy or with low health

        elif action == 'farm':
            if minions:
                # Base farming score
                score += 200

                # Bonus for multiple minions
                score += len(minions) * 30

                # Penalty if enemy is present (risky farming)
                if enemy:
                    score -= 150

                # Bonus if health is good for sustained farming
                if health > 50:
                    score += 50

        elif action == 'move_to_lane':
            # Default action when nothing else to do
            score += 50

            # Penalty if there are better opportunities
            if minions:
                score -= 100
            if enemy and health > config.HEALTH_LOW_THRESHOLD:
                score -= 50

        return max(0, score)  # Ensure non-negative scores

    def make_advanced_decision(self, health, minions, abilities, enemy, mana=50, gold=0, level=1):
        """Advanced decision making using the DecisionEngine"""
        game_state = GameState(
            health=health,
            mana=mana,
            minions=minions,
            enemy=enemy,
            abilities=abilities,
            gold=gold,
            level=level
        )

        return self.decision_engine.make_optimal_decision(game_state)

    def perform_action(self, action, minions=None, enemy=None):
        """Perform game actions"""
        if action == "recall":
            self.actions.recall()
        elif action == "farm":
            if minions:
                self.actions.move_mouse_to(minions[0][0], minions[0][1])
                self.actions.attack()
        elif action == "fight_champion":
            if enemy:
                self.actions.move_mouse_to(enemy[0], enemy[1])
                self.actions.use_ability('Q')
                self.actions.attack()
        elif action == "move_to_lane":
            self.actions.move_character('forward')

    def run(self):
        """Main bot loop"""
        if not self.find_game_window():
            print("Game window not found. Make sure League of Legends is running.")
            return

        print("Bot started. Press Ctrl+C to stop.")
        try:
            while True:
                img = self.take_screenshot()
                if img is not None:
                    health = self.vision.detect_health(img)
                    minions = self.vision.detect_minions(img)
                    abilities = self.vision.detect_abilities(img)
                    enemy = self.vision.detect_enemy_champion(img)

                    # Choose between simple and advanced decision making
                    if hasattr(config, 'USE_ADVANCED_DECISION_ENGINE') and config.USE_ADVANCED_DECISION_ENGINE:
                        action = self.make_advanced_decision(health, minions, abilities, enemy)
                    else:
                        action = self.make_decision(health, minions, abilities, enemy)

                    self.perform_action(action, minions, enemy)
                time.sleep(config.SCREENSHOT_INTERVAL)
        except KeyboardInterrupt:
            print("Bot stopped.")

def main():
    bot = LoLBot()
    bot.run()

if __name__ == "__main__":
    main()