import pyautogui
import cv2
import numpy as np
from PIL import Image
import pytesseract
import mss
import time
import sys
import os
from vision import Vision
from actions import Actions
import config

# Disclaimer: Automating League of Legends violates Riot's Terms of Service
# This bot is for educational purposes only. Use at your own risk.
print("WARNING: Automating League of Legends is against Riot's Terms of Service.")
print("This may result in permanent account bans. Proceed with caution.")

# Configure pytesseract (adjust path if needed)
pytesseract.pytesseract.tesseract_cmd = config.TESSERACT_PATH

class LoLBot:
    def __init__(self):
        self.game_window = None
        self.sct = mss.mss()
        self.vision = Vision()
        self.actions = None
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1

    def find_game_window(self):
        """Find the League of Legends window"""
        try:
            # This is a placeholder - need to implement window detection
            windows = pyautogui.getWindowsWithTitle(config.GAME_WINDOW_TITLE)
            if windows:
                self.game_window = windows[0]
                self.game_window.activate()
                self.actions = Actions(self.game_window)
                return True
            return False
        except Exception as e:
            print(f"Error finding game window: {e}")
            return False

    def take_screenshot(self):
        """Take screenshot of game area"""
        if self.game_window:
            bbox = (self.game_window.left, self.game_window.top,
                   self.game_window.left + self.game_window.width,
                   self.game_window.top + self.game_window.height)
            screenshot = self.sct.grab(bbox)
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            return np.array(img)
        return None

    def make_decision(self, health, minions, abilities, enemy):
        """Basic decision making logic"""
        if health < config.HEALTH_LOW_THRESHOLD:
            return "recall"
        elif enemy:
            return "fight_champion"
        elif minions:
            return "farm"
        else:
            return "move_to_lane"

    def perform_action(self, action, minions=None, enemy=None):
        """Perform game actions"""
        if action == "recall":
            self.actions.recall()
        elif action == "farm":
            if minions:
                self.actions.move_mouse_to(minions[0][0], minions[0][1])
                self.actions.attack()
        elif action == "fight_champion":
            if enemy:
                self.actions.move_mouse_to(enemy[0], enemy[1])
                self.actions.use_ability('Q')
                self.actions.attack()
        elif action == "move_to_lane":
            self.actions.move_character('forward')

    def run(self):
        """Main bot loop"""
        if not self.find_game_window():
            print("Game window not found. Make sure League of Legends is running.")
            return

        print("Bot started. Press Ctrl+C to stop.")
        try:
            while True:
                img = self.take_screenshot()
                if img is not None:
                    health = self.vision.detect_health(img)
                    minions = self.vision.detect_minions(img)
                    abilities = self.vision.detect_abilities(img)
                    enemy = self.vision.detect_enemy_champion(img)
                    action = self.make_decision(health, minions, abilities, enemy)
                    self.perform_action(action, minions, enemy)
                time.sleep(config.SCREENSHOT_INTERVAL)
        except KeyboardInterrupt:
            print("Bot stopped.")

def main():
    bot = LoLBot()
    bot.run()

if __name__ == "__main__":
    main()