# Configuration file for <PERSON><PERSON> Bot

# Window titles to search for
GAME_WINDOW_TITLE = "League of Legends"
CLIENT_WINDOW_TITLE = "League of Legends"
RIOT_CLIENT_TITLE = "Riot Client"

# Client automation settings
AUTO_START_GAME = True
GAME_MODE = "intro_bots"  # intro_bots, beginner_bots, intermediate_bots
PREFERRED_LANE = "bot"  # top, jungle, mid, bot, support
RANDOM_CHAMPION = True

# Key bindings (adjust based on your settings)
KEY_BINDINGS = {
    'Q': 'q',
    'W': 'w',
    'E': 'e',
    'R': 'r',
    'Flash': 'd',  # Summoner spell 1
    'Teleport': 'f',  # Summoner spell 2
    'Recall': 'b',
    'Shop': 'p',
    'Move Forward': 'w',
    'Move Backward': 's',
    'Move Left': 'a',
    'Move Right': 'd'
}

# Health thresholds
HEALTH_LOW_THRESHOLD = 20  # Recall if health below this
HEALTH_CRITICAL_THRESHOLD = 10  # Emergency recall
HEALTH_SAFE_THRESHOLD = 60  # Consider aggressive actions above this

# Decision-making weights
WEIGHTS = {
    'SAFETY': 10,      # Survival is top priority
    'COMBAT': 8,       # Fighting when advantageous
    'FARMING': 7,      # Economic growth
    'POSITIONING': 5,  # Map positioning
    'OBJECTIVES': 9    # Towers, dragons, baron
}

# Combat thresholds
MIN_HEALTH_FOR_COMBAT = 30
MIN_ABILITIES_FOR_COMBAT = 2  # Minimum abilities available to engage

# Timing
ACTION_DELAY = 0.3  # Delay between actions in seconds
SCREENSHOT_INTERVAL = 0.3  # How often to take screenshots (faster for better responsiveness)

# CV Settings
HEALTH_BAR_REGION = (100, 50, 300, 20)  # x, y, width, height relative to window
ABILITY_BAR_REGION = (400, 600, 400, 50)

# Enemy detection by red color (health bars, minions, champions)
# Red color in HSV (Hue, Saturation, Value)
RED_COLOR_LOWER = [0, 100, 100]    # Lower red range
RED_COLOR_UPPER = [10, 255, 255]   # Upper red range
RED_COLOR_LOWER2 = [160, 100, 100] # Second red range (HSV wraps around)
RED_COLOR_UPPER2 = [180, 255, 255] # Upper second red range

# Alternative red detection for different lighting
RED_HEALTH_BAR_LOWER = [0, 150, 150]  # More specific for health bars
RED_HEALTH_BAR_UPPER = [8, 255, 255]

# Minion detection (both ally and enemy)
MINION_COLOR_LOWER = [90, 50, 50]  # HSV for blue minions
MINION_COLOR_UPPER = [130, 255, 255]

# Minimap settings
MINIMAP_REGION = (1050, 650, 200, 200)  # x, y, width, height
LANE_POSITIONS = {
    'top': (1100, 680),
    'jungle': (1130, 720),
    'mid': (1150, 750),
    'bot': (1180, 780),
    'support': (1180, 780)
}

# Tesseract path (for OCR)
TESSERACT_PATH = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Debug mode
DEBUG = True

# Decision engine settings
USE_ADVANCED_DECISION_ENGINE = True  # Set to False to use simple decision making