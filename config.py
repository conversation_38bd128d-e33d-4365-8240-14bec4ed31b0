# Configuration file for <PERSON><PERSON> Bot

# Window title to search for
GAME_WINDOW_TITLE = "League of Legends"

# Key bindings (adjust based on your settings)
KEY_BINDINGS = {
    'Q': 'q',
    'W': 'w',
    'E': 'e',
    'R': 'r',
    'Flash': 'd',  # Summoner spell 1
    'Teleport': 'f',  # Summoner spell 2
    'Recall': 'b',
    'Shop': 'p',
    'Move Forward': 'w',
    'Move Backward': 's',
    'Move Left': 'a',
    'Move Right': 'd'
}

# Health thresholds
HEALTH_LOW_THRESHOLD = 20  # Recall if health below this
HEALTH_CRITICAL_THRESHOLD = 10  # Emergency recall

# Timing
ACTION_DELAY = 0.5  # Delay between actions in seconds
SCREENSHOT_INTERVAL = 0.5  # How often to take screenshots

# CV Settings
HEALTH_BAR_REGION = (100, 50, 300, 20)  # x, y, width, height relative to window
ABILITY_BAR_REGION = (400, 600, 400, 50)
MINION_COLOR_LOWER = [90, 50, 50]  # HSV for blue minions
MINION_COLOR_UPPER = [130, 255, 255]

# Tesseract path (for OCR)
TESSERACT_PATH = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Debug mode
DEBUG = True