"""
Test script for color detection
Use this to test and calibrate red color detection
"""

import cv2
import numpy as np
import mss
import pya<PERSON>gui
from PIL import Image
import config

def test_red_detection():
    """Test red color detection on current screen"""
    print("Testing red color detection...")
    print("Press 'q' to quit, 's' to save current frame")
    
    sct = mss.mss()
    
    # Get screen dimensions
    monitor = sct.monitors[1]  # Primary monitor
    
    while True:
        # Take screenshot
        screenshot = sct.grab(monitor)
        img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
        img_np = np.array(img)
        
        # Convert to HSV
        hsv = cv2.cvtColor(img_np, cv2.COLOR_RGB2HSV)
        
        # Create red masks
        lower1 = np.array(config.RED_COLOR_LOWER)
        upper1 = np.array(config.RED_COLOR_UPPER)
        lower2 = np.array(config.RED_COLOR_LOWER2)
        upper2 = np.array(config.RED_COLOR_UPPER2)
        
        mask1 = cv2.inRange(hsv, lower1, upper1)
        mask2 = cv2.inRange(hsv, lower2, upper2)
        red_mask = cv2.bitwise_or(mask1, mask2)
        
        # Find contours
        contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Draw contours on original image
        result = img_np.copy()
        for cnt in contours:
            if cv2.contourArea(cnt) > 50:  # Filter small areas
                x, y, w, h = cv2.boundingRect(cnt)
                cv2.rectangle(result, (x, y), (x+w, y+h), (0, 255, 0), 2)
                cv2.putText(result, f"Red {int(cv2.contourArea(cnt))}", 
                           (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # Resize for display
        height, width = result.shape[:2]
        if width > 1200:
            scale = 1200 / width
            new_width = int(width * scale)
            new_height = int(height * scale)
            result = cv2.resize(result, (new_width, new_height))
            red_mask = cv2.resize(red_mask, (new_width, new_height))
        
        # Show results
        cv2.imshow('Red Detection', result)
        cv2.imshow('Red Mask', red_mask)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            cv2.imwrite('red_detection_test.png', result)
            cv2.imwrite('red_mask_test.png', red_mask)
            print("Images saved!")
    
    cv2.destroyAllWindows()

def calibrate_red_colors():
    """Interactive calibration for red color ranges"""
    print("Red Color Calibration")
    print("Adjust the trackbars to fine-tune red detection")
    
    sct = mss.mss()
    monitor = sct.monitors[1]
    
    # Create window and trackbars
    cv2.namedWindow('Calibration')
    cv2.createTrackbar('H_min', 'Calibration', config.RED_COLOR_LOWER[0], 180, lambda x: None)
    cv2.createTrackbar('S_min', 'Calibration', config.RED_COLOR_LOWER[1], 255, lambda x: None)
    cv2.createTrackbar('V_min', 'Calibration', config.RED_COLOR_LOWER[2], 255, lambda x: None)
    cv2.createTrackbar('H_max', 'Calibration', config.RED_COLOR_UPPER[0], 180, lambda x: None)
    cv2.createTrackbar('S_max', 'Calibration', config.RED_COLOR_UPPER[1], 255, lambda x: None)
    cv2.createTrackbar('V_max', 'Calibration', config.RED_COLOR_UPPER[2], 255, lambda x: None)
    
    while True:
        # Get trackbar values
        h_min = cv2.getTrackbarPos('H_min', 'Calibration')
        s_min = cv2.getTrackbarPos('S_min', 'Calibration')
        v_min = cv2.getTrackbarPos('V_min', 'Calibration')
        h_max = cv2.getTrackbarPos('H_max', 'Calibration')
        s_max = cv2.getTrackbarPos('S_max', 'Calibration')
        v_max = cv2.getTrackbarPos('V_max', 'Calibration')
        
        # Take screenshot
        screenshot = sct.grab(monitor)
        img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
        img_np = np.array(img)
        
        # Convert to HSV
        hsv = cv2.cvtColor(img_np, cv2.COLOR_RGB2HSV)
        
        # Create mask with current values
        lower = np.array([h_min, s_min, v_min])
        upper = np.array([h_max, s_max, v_max])
        mask = cv2.inRange(hsv, lower, upper)
        
        # Apply mask
        result = cv2.bitwise_and(img_np, img_np, mask=mask)
        
        # Resize for display
        height, width = result.shape[:2]
        if width > 800:
            scale = 800 / width
            new_width = int(width * scale)
            new_height = int(height * scale)
            result = cv2.resize(result, (new_width, new_height))
            mask = cv2.resize(mask, (new_width, new_height))
        
        # Show results
        cv2.imshow('Calibration', result)
        cv2.imshow('Mask', mask)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            print(f"Current values:")
            print(f"RED_COLOR_LOWER = [{h_min}, {s_min}, {v_min}]")
            print(f"RED_COLOR_UPPER = [{h_max}, {s_max}, {v_max}]")
    
    cv2.destroyAllWindows()

def test_game_window_detection():
    """Test game window detection"""
    print("Testing game window detection...")
    
    try:
        windows = pyautogui.getWindowsWithTitle(config.GAME_WINDOW_TITLE)
        if windows:
            print(f"Found {len(windows)} windows with title '{config.GAME_WINDOW_TITLE}':")
            for i, window in enumerate(windows):
                print(f"  Window {i+1}: {window.width}x{window.height} at ({window.left}, {window.top})")
                
            # Select largest window
            largest = max(windows, key=lambda w: w.width * w.height)
            print(f"Largest window: {largest.width}x{largest.height}")
            
            # Activate and take screenshot
            largest.activate()
            return True
        else:
            print(f"No windows found with title '{config.GAME_WINDOW_TITLE}'")
            
            # List all windows
            print("Available windows:")
            all_windows = pyautogui.getAllWindows()
            for window in all_windows[:10]:  # Show first 10
                if window.title:
                    print(f"  '{window.title}' - {window.width}x{window.height}")
            
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    """Main test function"""
    print("=== League of Legends Bot - Color Detection Test ===")
    print("1. Test red detection on current screen")
    print("2. Calibrate red color ranges")
    print("3. Test game window detection")
    print("4. Exit")
    
    while True:
        try:
            choice = input("\nEnter choice (1-4): ").strip()
            
            if choice == "1":
                test_red_detection()
            elif choice == "2":
                calibrate_red_colors()
            elif choice == "3":
                test_game_window_detection()
            elif choice == "4":
                break
            else:
                print("Invalid choice")
        except KeyboardInterrupt:
            print("\nExiting...")
            break

if __name__ == "__main__":
    main()
