#!/usr/bin/env python3
"""
Script de inicialização simples para o Bot League of Legends
"""

import sys
import os

def main():
    print("=" * 60)
    print("🎮 BOT LEAGUE OF LEGENDS - AUTOMAÇÃO COMPLETA")
    print("=" * 60)
    print()
    print("⚠️  AVISO: Este bot é apenas para fins educacionais!")
    print("⚠️  O uso pode violar os Termos de Serviço da Riot Games!")
    print("⚠️  Use por sua própria conta e risco!")
    print()
    print("📋 INSTRUÇÕES:")
    print("1. Certifique-se que o League of Legends está aberto")
    print("2. Para automação completa: tenha o cliente aberto")
    print("3. Para apenas jogo: esteja já em uma partida")
    print()
    print("🎯 FUNCIONALIDADES:")
    print("✅ Detecção automática de inimigos por cor vermelha")
    print("✅ Farm automático de minions com clique direito")
    print("✅ Ataque a campeões inimigos")
    print("✅ Navegação automática para rota")
    print("✅ Sistema de segurança (recall)")
    print()
    
    # Verificar se os arquivos necessários existem
    required_files = [
        'lol_bot_complete.py',
        'config.py',
        'vision.py',
        'actions.py',
        'client_automation.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ERRO: Arquivos necessários não encontrados:")
        for file in missing_files:
            print(f"   - {file}")
        print()
        print("Certifique-se de que todos os arquivos estão na mesma pasta.")
        input("Pressione Enter para sair...")
        return
    
    print("✅ Todos os arquivos necessários encontrados!")
    print()
    
    # Menu de opções
    while True:
        print("-" * 40)
        print("ESCOLHA UMA OPÇÃO:")
        print("1. 🚀 Iniciar Bot Completo (Cliente + Jogo)")
        print("2. 🎮 Iniciar Bot Apenas no Jogo")
        print("3. 🔧 Testar Detecção de Cores")
        print("4. ❌ Sair")
        print("-" * 40)
        
        try:
            choice = input("Digite sua escolha (1-4): ").strip()
            
            if choice == "1":
                print("\n🚀 Iniciando automação completa...")
                print("Certifique-se de que o cliente do LoL está aberto!")
                input("Pressione Enter para continuar ou Ctrl+C para cancelar...")
                
                # Importar e executar bot completo
                try:
                    from lol_bot_complete import CompleteLolBot
                    bot = CompleteLolBot()
                    bot.run_complete_automation()
                except KeyboardInterrupt:
                    print("\n⏹️  Bot interrompido pelo usuário")
                except Exception as e:
                    print(f"\n❌ Erro ao executar bot: {e}")
                
            elif choice == "2":
                print("\n🎮 Iniciando bot apenas no jogo...")
                print("Certifique-se de que você está em uma partida!")
                input("Pressione Enter para continuar ou Ctrl+C para cancelar...")
                
                try:
                    from lol_bot_complete import CompleteLolBot
                    bot = CompleteLolBot()
                    bot.run_game_only()
                except KeyboardInterrupt:
                    print("\n⏹️  Bot interrompido pelo usuário")
                except Exception as e:
                    print(f"\n❌ Erro ao executar bot: {e}")
                
            elif choice == "3":
                print("\n🔧 Iniciando teste de detecção de cores...")
                print("Use este modo para calibrar a detecção de inimigos")
                
                try:
                    from test_color_detection import main as test_main
                    test_main()
                except KeyboardInterrupt:
                    print("\n⏹️  Teste interrompido pelo usuário")
                except Exception as e:
                    print(f"\n❌ Erro ao executar teste: {e}")
                
            elif choice == "4":
                print("\n👋 Saindo... Até logo!")
                break
                
            else:
                print("\n❌ Opção inválida! Digite 1, 2, 3 ou 4.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Saindo... Até logo!")
            break
        except Exception as e:
            print(f"\n❌ Erro inesperado: {e}")
            
        print("\n" + "="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Programa encerrado pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro fatal: {e}")
        input("Pressione Enter para sair...")
