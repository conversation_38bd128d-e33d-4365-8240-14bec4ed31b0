"""
League of Legends Client Automation
Handles client navigation, champion selection, and game startup
"""

import pyautogui
import time
import random
import cv2
import numpy as np
from PIL import Image
import mss
import config

class ClientAutomation:
    def __init__(self):
        self.sct = mss.mss()
        self.client_window = None
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
        
    def find_client_window(self):
        """Find League of Legends client window"""
        try:
            print("Procurando janela do cliente...")

            # Lista todos os títulos de janela disponíveis
            all_windows = pyautogui.getAllWindows()
            print(f"Encontradas {len(all_windows)} janelas abertas")

            # Mostra as primeiras 10 janelas para debug
            print("Janelas disponíveis:")
            for i, window in enumerate(all_windows[:15]):
                if window.title.strip():  # Só mostra janelas com título
                    print(f"  {i+1}. '{window.title}' - {window.width}x{window.height}")

            # Try different window titles
            possible_titles = [
                "League of Legends",
                "Riot Client",
                "League of Legends (TM) Client",
                "LoL",
                "Riot Games"
            ]

            # Procura por títulos exatos
            for title in possible_titles:
                windows = pyautogui.getWindowsWithTitle(title)
                if windows:
                    # Pega a maior janela (provavelmente o cliente principal)
                    largest_window = max(windows, key=lambda w: w.width * w.height)
                    self.client_window = largest_window
                    print(f"Encontrou janela: '{title}' - {largest_window.width}x{largest_window.height}")

                    # Tenta ativar a janela de forma mais segura
                    try:
                        largest_window.activate()
                        time.sleep(2)
                        return True
                    except Exception as activate_error:
                        print(f"Erro ao ativar janela: {activate_error}")
                        # Continua mesmo se não conseguir ativar
                        return True

            # Se não encontrou por título exato, procura por palavras-chave
            print("Procurando por palavras-chave...")
            for window in all_windows:
                title_lower = window.title.lower()
                if any(keyword in title_lower for keyword in ['league', 'riot', 'lol']):
                    if window.width > 800 and window.height > 600:  # Janela grande o suficiente
                        self.client_window = window
                        print(f"Encontrou por palavra-chave: '{window.title}' - {window.width}x{window.height}")
                        try:
                            window.activate()
                            time.sleep(2)
                        except:
                            pass  # Ignora erro de ativação
                        return True

            print("❌ Janela do cliente não encontrada!")
            print("Certifique-se de que o League of Legends está aberto.")
            return False

        except Exception as e:
            print(f"Erro ao procurar janela do cliente: {e}")
            print("Tentando método alternativo...")

            # Método alternativo: usar a janela ativa
            try:
                import win32gui
                active_window = win32gui.GetForegroundWindow()
                if active_window:
                    title = win32gui.GetWindowText(active_window)
                    print(f"Usando janela ativa: '{title}'")
                    # Criar um objeto window-like
                    class SimpleWindow:
                        def __init__(self, hwnd):
                            self.hwnd = hwnd
                            rect = win32gui.GetWindowRect(hwnd)
                            self.left = rect[0]
                            self.top = rect[1]
                            self.width = rect[2] - rect[0]
                            self.height = rect[3] - rect[1]
                            self.title = win32gui.GetWindowText(hwnd)

                        def activate(self):
                            win32gui.SetForegroundWindow(self.hwnd)

                    self.client_window = SimpleWindow(active_window)
                    return True
            except ImportError:
                print("win32gui não disponível, usando método básico")
            except Exception as alt_error:
                print(f"Método alternativo falhou: {alt_error}")

            return False
    
    def take_client_screenshot(self):
        """Take screenshot of client window"""
        if self.client_window:
            bbox = (self.client_window.left, self.client_window.top,
                   self.client_window.left + self.client_window.width,
                   self.client_window.top + self.client_window.height)
            screenshot = self.sct.grab(bbox)
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            return np.array(img)
        return None
    
    def click_on_text(self, text, confidence=0.8):
        """Click on text found on screen"""
        try:
            location = pyautogui.locateOnScreen(text, confidence=confidence)
            if location:
                center = pyautogui.center(location)
                pyautogui.click(center)
                return True
        except:
            pass
        return False
    
    def click_on_color(self, color_bgr, threshold=30):
        """Click on a specific color on screen"""
        img = self.take_client_screenshot()
        if img is None:
            return False
        
        # Convert BGR to RGB for comparison
        target_color = color_bgr[::-1]  # BGR to RGB
        
        # Find pixels close to target color
        diff = np.abs(img - target_color)
        mask = np.all(diff < threshold, axis=2)
        
        if np.any(mask):
            # Find first matching pixel
            y, x = np.where(mask)
            if len(x) > 0 and len(y) > 0:
                # Click on the first match
                click_x = self.client_window.left + x[0]
                click_y = self.client_window.top + y[0]
                pyautogui.click(click_x, click_y)
                return True
        
        return False
    
    def start_intro_bot_game(self):
        """Start an intro bot game"""
        print("Starting intro bot game...")
        
        # Navigate to Play menu
        self.safe_click(640, 100)  # Play button (approximate)
        time.sleep(2)
        
        # Click on Training
        self.safe_click(200, 200)  # Training option
        time.sleep(1)
        
        # Click on Intro Bots
        self.safe_click(400, 300)  # Intro Bots option
        time.sleep(1)
        
        # Confirm game mode
        self.safe_click(640, 600)  # Confirm/Find Match button
        time.sleep(2)
        
        print("Searching for intro bot game...")
        return True
    
    def select_random_champion(self):
        """Select a random champion during champion select"""
        print("Selecting random champion...")
        
        # Wait for champion select screen
        time.sleep(5)
        
        # Try to find and click random champion button
        # This is usually in the champion select interface
        random_positions = [
            (400, 300), (500, 300), (600, 300),  # Champion grid
            (400, 400), (500, 400), (600, 400),
            (400, 500), (500, 500), (600, 500)
        ]
        
        # Click on a random champion position
        random_pos = random.choice(random_positions)
        self.safe_click(random_pos[0], random_pos[1])
        time.sleep(1)
        
        # Lock in champion
        self.safe_click(640, 650)  # Lock in button (approximate)
        time.sleep(2)
        
        print("Champion selected and locked in")
        return True
    
    def wait_for_game_start(self):
        """Wait for the game to start"""
        print("Waiting for game to start...")
        
        # Wait for loading screen and game start
        max_wait = 120  # 2 minutes max wait
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            # Check if we're in game by looking for game UI elements
            img = self.take_client_screenshot()
            if img is not None:
                # Look for minimap or other game UI elements
                # This is a simple check - in practice you'd look for specific UI elements
                if self.is_in_game(img):
                    print("Game started!")
                    return True
            
            time.sleep(2)
        
        print("Timeout waiting for game to start")
        return False
    
    def is_in_game(self, img):
        """Check if we're currently in game"""
        # Simple check - look for minimap region
        # In a real implementation, you'd check for specific UI elements
        height, width = img.shape[:2]
        
        # Check if image is large enough to be in-game
        if width > 1000 and height > 700:
            # Look for darker regions that might be the minimap
            minimap_region = img[650:850, 1050:1250] if width > 1250 and height > 850 else None
            if minimap_region is not None:
                # Simple darkness check for minimap
                avg_brightness = np.mean(minimap_region)
                return avg_brightness < 100  # Minimap is usually darker
        
        return False
    
    def safe_click(self, x, y):
        """Safely click at coordinates relative to client window"""
        if self.client_window:
            screen_x = self.client_window.left + x
            screen_y = self.client_window.top + y
            pyautogui.click(screen_x, screen_y)
        else:
            pyautogui.click(x, y)
    
    def navigate_to_lane(self, lane="bot"):
        """Click on minimap to navigate to specified lane"""
        print(f"Navigating to {lane} lane...")
        
        if lane in config.LANE_POSITIONS:
            pos = config.LANE_POSITIONS[lane]
            self.safe_click(pos[0], pos[1])
            time.sleep(1)
            return True
        
        return False
    
    def full_automation_sequence(self):
        """Complete automation sequence from client to game"""
        print("Starting full automation sequence...")
        
        # Step 1: Find client
        if not self.find_client_window():
            return False
        
        # Step 2: Start game
        if config.AUTO_START_GAME:
            if not self.start_intro_bot_game():
                print("Failed to start game")
                return False
        
        # Step 3: Select champion
        if config.RANDOM_CHAMPION:
            if not self.select_random_champion():
                print("Failed to select champion")
                return False
        
        # Step 4: Wait for game start
        if not self.wait_for_game_start():
            print("Failed to detect game start")
            return False
        
        # Step 5: Navigate to lane
        time.sleep(10)  # Wait for game to fully load
        self.navigate_to_lane(config.PREFERRED_LANE)
        
        print("Automation sequence completed successfully!")
        return True

def main():
    """Test the client automation"""
    automation = ClientAutomation()
    automation.full_automation_sequence()

if __name__ == "__main__":
    main()
