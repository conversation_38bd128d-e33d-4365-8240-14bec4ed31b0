"""
Bot League of Legends com Template Matching
Usa imagens de referência para detectar minions e segue alvos em movimento
"""

import pyautogui
import cv2
import numpy as np
from PIL import Image
import mss
import time
import random

from template_detection import TemplateDetector
from actions import Actions
import config

class BotTemplateMatching:
    def __init__(self):
        self.game_window = None
        self.sct = mss.mss()
        self.template_detector = TemplateDetector()
        self.actions = None
        self.current_target = None
        self.target_lock_time = 0
        self.last_attack_time = 0
        
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.05  # Mais rápido para seguimento
        
        print("🎯 Bot com Template Matching Iniciado")
        print("📸 Usando imagens de referência para detecção")
        
    def encontrar_janela_jogo(self):
        """Encontra a janela do jogo"""
        print("🔍 Procurando janela do jogo...")
        
        try:
            all_windows = pyautogui.getAllWindows()
            
            # Procura por janelas grandes que podem ser o jogo
            game_candidates = []
            for window in all_windows:
                if window.width >= 1024 and window.height >= 768:
                    title_lower = window.title.lower()
                    if any(keyword in title_lower for keyword in ['league', 'riot', 'lol']) or not window.title.strip():
                        game_candidates.append(window)
            
            if game_candidates:
                self.game_window = max(game_candidates, key=lambda w: w.width * w.height)
                print(f"✅ Janela encontrada: {self.game_window.width}x{self.game_window.height}")
                
                try:
                    self.game_window.activate()
                    time.sleep(1)
                except:
                    print("⚠️  Não foi possível ativar janela, continuando...")
                
                self.actions = Actions(self.game_window)
                return True
            
            # Fallback: usa tela toda
            return self.usar_tela_completa()
            
        except Exception as e:
            print(f"❌ Erro: {e}")
            return self.usar_tela_completa()
    
    def usar_tela_completa(self):
        """Usa a tela completa como área de jogo"""
        class JanelaCompleta:
            def __init__(self):
                self.left = 0
                self.top = 0
                self.width = pyautogui.size().width
                self.height = pyautogui.size().height
                self.title = "Tela Completa"
            def activate(self): pass
        
        self.game_window = JanelaCompleta()
        self.actions = Actions(self.game_window)
        print("✅ Usando tela completa")
        return True
    
    def take_screenshot(self):
        """Tira screenshot da área do jogo"""
        try:
            if self.game_window:
                bbox = (self.game_window.left, self.game_window.top,
                       self.game_window.left + self.game_window.width,
                       self.game_window.top + self.game_window.height)
            else:
                bbox = (0, 0, pyautogui.size().width, pyautogui.size().height)
            
            screenshot = self.sct.grab(bbox)
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            return np.array(img)
        except Exception as e:
            print(f"❌ Erro no screenshot: {e}")
            return None
    
    def attack_target_with_prediction(self, target_pos, target_id=None):
        """
        Ataca um alvo considerando predição de movimento
        """
        x, y = target_pos
        
        # Se temos ID do alvo, usa predição de movimento
        if target_id and target_id in self.template_detector.target_history:
            predicted_pos = self.template_detector.predict_target_position(target_id, time_ahead=0.3)
            if predicted_pos:
                pred_x, pred_y = predicted_pos
                
                # Usa posição predita se não estiver muito longe
                distance_to_prediction = np.sqrt((pred_x - x)**2 + (pred_y - y)**2)
                if distance_to_prediction < 100:  # Máximo 100 pixels de predição
                    x, y = pred_x, pred_y
                    print(f"🎯 Atacando posição predita: ({x}, {y})")
        
        # Clique direito para atacar
        self.actions.attack_move(x, y)
        self.last_attack_time = time.time()
        
        return True
    
    def select_best_target(self, screenshot):
        """
        Seleciona o melhor alvo para atacar
        Prioriza alvos próximos e em movimento
        """
        enemy_detections = self.template_detector.detect_enemy_minions(screenshot)
        
        if not enemy_detections:
            self.current_target = None
            return None
        
        current_time = time.time()
        
        # Se já temos um alvo e ele ainda está visível, continua com ele
        if self.current_target and (current_time - self.target_lock_time) < 3.0:
            target_x, target_y = self.current_target
            
            # Verifica se o alvo atual ainda está próximo de alguma detecção
            for x, y, conf in enemy_detections:
                distance = np.sqrt((x - target_x)**2 + (y - target_y)**2)
                if distance < 80:  # Alvo ainda está próximo
                    self.current_target = (x, y)
                    return (x, y, conf)
        
        # Seleciona novo alvo
        best_target = self.template_detector.get_best_enemy_target(screenshot)
        
        if best_target:
            x, y, conf = best_target
            self.current_target = (x, y)
            self.target_lock_time = current_time
            print(f"🎯 Novo alvo selecionado: ({x}, {y}) confiança: {conf:.2f}")
            return best_target
        
        return None
    
    def farm_enemies(self, screenshot):
        """
        Sistema principal de farm com seguimento de alvos
        """
        target = self.select_best_target(screenshot)
        
        if not target:
            return False
        
        x, y, confidence = target
        
        # Encontra o ID do alvo para predição de movimento
        target_id = None
        for tid, data in self.template_detector.target_history.items():
            tx, ty = data['position']
            distance = np.sqrt((tx - x)**2 + (ty - y)**2)
            if distance < 50:
                target_id = tid
                break
        
        # Ataca com predição de movimento
        success = self.attack_target_with_prediction((x, y), target_id)
        
        if success:
            # Usa habilidade ocasionalmente
            if random.random() < 0.2:  # 20% de chance
                habilidade = random.choice(['Q', 'W', 'E'])
                self.actions.use_ability(habilidade)
                print(f"⚡ Usando habilidade {habilidade}")
        
        return success
    
    def navigate_to_lane(self):
        """Navega para a rota usando minimapa"""
        try:
            if config.PREFERRED_LANE in config.LANE_POSITIONS:
                pos = config.LANE_POSITIONS[config.PREFERRED_LANE]
                self.actions.click_at(pos[0], pos[1])
                print(f"🗺️  Navegando para {config.PREFERRED_LANE}")
                return True
        except Exception as e:
            print(f"❌ Erro na navegação: {e}")
        return False
    
    def show_debug_window(self, screenshot):
        """Mostra janela de debug com detecções"""
        debug_img = self.template_detector.draw_detections(screenshot)
        
        # Redimensiona para caber na tela
        height, width = debug_img.shape[:2]
        if width > 1200:
            scale = 1200 / width
            new_width = int(width * scale)
            new_height = int(height * scale)
            debug_img = cv2.resize(debug_img, (new_width, new_height))
        
        cv2.imshow('Bot Debug - Template Matching', debug_img)
        cv2.waitKey(1)
    
    def loop_principal(self):
        """Loop principal do bot"""
        print("🚀 Iniciando loop principal...")
        print("Pressione Ctrl+C para parar")
        print("Pressione 'd' na janela de debug para alternar visualização")
        
        show_debug = True
        frame_count = 0
        
        try:
            while True:
                frame_count += 1
                
                # Screenshot
                img = self.take_screenshot()
                if img is None:
                    time.sleep(0.5)
                    continue
                
                # Debug visual (a cada 3 frames para performance)
                if show_debug and frame_count % 3 == 0:
                    self.show_debug_window(img)
                
                # Navegação (a cada 50 frames)
                if frame_count % 50 == 0:
                    self.navigate_to_lane()
                
                # Farm principal
                if self.farm_enemies(img):
                    print("⚔️  Atacando inimigo detectado")
                else:
                    # Se não há inimigos, move aleatoriamente
                    if random.random() < 0.05:  # 5% de chance
                        x = random.randint(400, 800)
                        y = random.randint(300, 600)
                        self.actions.click_at(x, y)
                
                # Recall ocasional (simulado)
                if random.random() < 0.002:  # 0.2% de chance
                    print("🏠 Fazendo recall...")
                    self.actions.recall()
                    time.sleep(8)
                
                # Controle de velocidade
                time.sleep(0.1)  # 10 FPS para responsividade
                
        except KeyboardInterrupt:
            print("\n⏹️  Bot parado pelo usuário")
        except Exception as e:
            print(f"❌ Erro no loop: {e}")
        finally:
            cv2.destroyAllWindows()
    
    def executar(self):
        """Executa o bot"""
        print("=" * 60)
        print("🎯 BOT LEAGUE OF LEGENDS - TEMPLATE MATCHING")
        print("=" * 60)
        print()
        print("📋 FUNCIONALIDADES:")
        print("✅ Detecção por imagens de referência")
        print("✅ Seguimento de alvos em movimento")
        print("✅ Predição de posição futura")
        print("✅ Clique direito automático")
        print("✅ Debug visual em tempo real")
        print()
        print("📸 Certifique-se de que as imagens estão em:")
        print("   - img/minions_aliados.png")
        print("   - img/minions_inimigos.png")
        print()
        
        input("Pressione Enter para continuar...")
        
        # Verifica se templates foram carregados
        if (self.template_detector.minion_aliado_template is None or 
            self.template_detector.minion_inimigo_template is None):
            print("❌ Erro: Templates não carregados!")
            print("Certifique-se de que as imagens estão na pasta 'img/'")
            return False
        
        # Encontra janela
        if not self.encontrar_janela_jogo():
            print("❌ Não foi possível encontrar janela do jogo")
            return False
        
        print("⏳ Aguardando 3 segundos...")
        time.sleep(3)
        
        # Inicia loop
        self.loop_principal()
        return True

def main():
    """Função principal"""
    bot = BotTemplateMatching()
    bot.executar()

if __name__ == "__main__":
    main()
