# 🔧 Solução de Problemas - Bot League of Legends

## ❌ Problema: "Error code from Windows: 6 - Identificador inválido"

### 🎯 **SOLUÇÃO RÁPIDA:**
Use o **Bot Simples** em vez do bot completo:

```bash
python start_bot.py
```
**Escolha a opção 3** (Bot Simples - Recomendado)

### 📋 **Passos para resolver:**

1. **Execute o diagnóstico:**
   ```bash
   python diagnostico.py
   ```
   - Escolha opção 5 (Diagnóstico completo)
   - Veja se o LoL é detectado

2. **Se o diagnóstico não encontrar o LoL:**
   - Certifique-se que o League of Legends está **realmente aberto**
   - Não minimize a janela do LoL
   - Tente reiniciar o cliente do LoL

3. **Use o Bot Simples:**
   ```bash
   python bot_simples.py
   ```
   Este bot é mais confiável e não depende da detecção específica de janela.

---

## 🎮 Como Usar o Bot Corretamente

### **Método Recomendado (Bot Simples):**

1. **Abra o League of Legends**
2. **Inicie uma partida contra bots** (modo introdução)
3. **Quando estiver no jogo, execute:**
   ```bash
   python start_bot.py
   ```
4. **Escolha opção 3** (Bot Simples)
5. **O bot começará a farmar automaticamente**

### **O que o Bot Simples faz:**
- ✅ Detecta inimigos por cor vermelha
- ✅ Faz farm com clique direito
- ✅ Ataca campeões inimigos
- ✅ Navega para a rota
- ✅ Funciona sem detecção específica de janela

---

## 🔍 Outros Problemas Comuns

### **Bot não detecta inimigos:**

1. **Calibre as cores:**
   ```bash
   python test_color_detection.py
   ```
   - Escolha opção 2 (Calibrar cores)
   - Ajuste até detectar inimigos vermelhos

2. **Ajuste as configurações gráficas do LoL:**
   - Use qualidade **Média** ou **Alta**
   - **Desabilite sombras** se necessário
   - Use resolução **1920x1080** se possível

### **Bot clica nos lugares errados:**

1. **Verifique a resolução:**
   - O bot funciona melhor em **1920x1080**
   - Use modo **Janela sem bordas** ou **Tela cheia**

2. **Ajuste as coordenadas no config.py:**
   ```python
   MINIMAP_REGION = (1050, 650, 200, 200)  # Ajuste conforme sua tela
   ```

### **Bot muito lento ou muito rápido:**

Ajuste no `config.py`:
```python
SCREENSHOT_INTERVAL = 0.3  # Mais rápido = menor valor
ACTION_DELAY = 0.3         # Mais rápido = menor valor
```

---

## 📊 Modos Disponíveis

### 1. **Bot Completo** (Opção 1)
- Automação total do cliente
- Pode ter problemas de detecção de janela
- **Use apenas se o diagnóstico funcionar**

### 2. **Bot Avançado** (Opção 2)
- Apenas in-game, sem automação do cliente
- Sistema de decisão complexo
- **Use se já estiver em partida**

### 3. **Bot Simples** (Opção 3) ⭐ **RECOMENDADO**
- Mais confiável
- Detecção robusta
- Funciona na maioria dos casos
- **Use este se tiver problemas**

### 4. **Teste de Cores** (Opção 4)
- Para calibrar detecção de inimigos
- **Use se bot não detecta inimigos**

### 5. **Diagnóstico** (Opção 5)
- Identifica problemas
- **Use se nada funcionar**

---

## ⚡ Dicas de Performance

### **Configurações Recomendadas do LoL:**
- **Resolução:** 1920x1080
- **Modo:** Janela sem bordas
- **Qualidade:** Média/Alta
- **Sombras:** Desabilitadas
- **Efeitos:** Médios

### **Sistema:**
- **Feche outros programas** pesados
- **Use SSD** se possível
- **RAM:** Mínimo 8GB recomendado

---

## 🆘 Se Nada Funcionar

### **Última tentativa:**

1. **Reinicie tudo:**
   - Feche o League of Legends
   - Reinicie o cliente
   - Execute o bot novamente

2. **Use modo manual:**
   ```bash
   python bot_simples.py
   ```
   - Este é o mais confiável
   - Funciona mesmo com problemas de janela

3. **Verifique dependências:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Execute diagnóstico completo:**
   ```bash
   python diagnostico.py
   ```

---

## 📞 Checklist de Solução

- [ ] League of Legends está aberto e visível
- [ ] Estou em uma partida (não no cliente)
- [ ] Usei o Bot Simples (opção 3)
- [ ] Calibrei as cores se necessário
- [ ] Configurações gráficas adequadas
- [ ] Resolução 1920x1080 ou similar
- [ ] Executei o diagnóstico

---

## 🎯 Resumo Rápido

**Problema com detecção de janela?**
→ Use **Bot Simples** (opção 3)

**Bot não detecta inimigos?**
→ Use **Teste de Cores** (opção 4)

**Nada funciona?**
→ Use **Diagnóstico** (opção 5)

**Quer simplicidade?**
→ Execute `python bot_simples.py` diretamente

---

**💡 Lembre-se:** O Bot Simples é a opção mais confiável e funciona na maioria dos casos!
