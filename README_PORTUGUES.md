# Bot League of Legends - Automação Completa

## ⚠️ AVISO IMPORTANTE
Este bot é apenas para fins educacionais. O uso de automação no League of Legends viola os Termos de Serviço da Riot Games e pode resultar em banimento permanente da conta. Use por sua própria conta e risco.

## 🚀 Funcionalidades

### Automação do Cliente
- ✅ Detecta automaticamente o cliente do League of Legends
- ✅ Inicia partida contra bots no modo introdução
- ✅ Seleciona campeão aleatório
- ✅ Entra na partida automaticamente

### Automação In-Game
- ✅ Detecta inimigos por cor vermelha (minions e campeões)
- ✅ Farm automático de minions com clique direito
- ✅ Ataque a campeões inimigos com clique direito
- ✅ Navegação automática para a rota selecionada via minimapa
- ✅ Sistema de segurança (recall quando vida baixa)

## 📋 Pré-requisitos

### Software Necessário
1. **Python 3.8+**
2. **League of Legends** instalado e funcionando
3. **Tesseract OCR** (opcional, para detecção de texto)

### Bibliotecas Python
```bash
pip install -r requirements.txt
```

## 🔧 Configuração

### 1. Configurar o arquivo `config.py`

```python
# Configurações principais
AUTO_START_GAME = True          # Iniciar jogo automaticamente
GAME_MODE = "intro_bots"        # Modo de jogo
PREFERRED_LANE = "bot"          # Rota preferida (top, jungle, mid, bot, support)
RANDOM_CHAMPION = True          # Selecionar campeão aleatório

# Detecção de cores (ajustar se necessário)
RED_COLOR_LOWER = [0, 100, 100]    # Cor vermelha mínima (HSV)
RED_COLOR_UPPER = [10, 255, 255]   # Cor vermelha máxima (HSV)
```

### 2. Calibrar Detecção de Cores (Recomendado)

Execute o script de teste para calibrar a detecção de inimigos vermelhos:

```bash
python test_color_detection.py
```

Escolha a opção 2 para calibrar as cores e ajuste os valores até que os inimigos sejam detectados corretamente.

## 🎮 Como Usar

### Modo 1: Automação Completa (Cliente + Jogo)

1. **Abra o cliente do League of Legends**
2. **Execute o bot:**
   ```bash
   python lol_bot_complete.py
   ```
3. **Escolha a opção 1** (Complete automation)
4. **O bot irá:**
   - Detectar o cliente
   - Iniciar uma partida contra bots
   - Selecionar um campeão aleatório
   - Entrar na partida
   - Navegar para a rota
   - Começar a farmar automaticamente

### Modo 2: Apenas In-Game (Pular Automação do Cliente)

1. **Inicie uma partida manualmente** (modo introdução contra bots)
2. **Quando estiver no jogo, execute:**
   ```bash
   python lol_bot_complete.py
   ```
3. **Escolha a opção 2** (Game only)
4. **O bot começará a farmar automaticamente**

## 🎯 Comportamento do Bot

### Prioridades de Ação
1. **Segurança:** Recall se vida < 10%
2. **Combate:** Atacar campeões inimigos (cor vermelha)
3. **Farm:** Atacar minions inimigos (cor vermelha)
4. **Movimento:** Navegar para a rota

### Detecção de Inimigos
- **Minions inimigos:** Detectados por cor vermelha na barra de vida
- **Campeões inimigos:** Detectados por cor vermelha na barra de vida
- **Ação:** Clique direito para atacar/farmar

### Navegação
- **Minimapa:** Clica no minimapa para navegar
- **Rota:** Configurável no `config.py`
- **Posicionamento:** Mantém-se na rota selecionada

## 🛠️ Solução de Problemas

### Bot não detecta o jogo
1. Verifique se o League of Legends está aberto
2. Execute `test_color_detection.py` opção 3 para testar detecção de janela
3. Ajuste `GAME_WINDOW_TITLE` no `config.py` se necessário

### Bot não detecta inimigos
1. Execute `test_color_detection.py` opção 1 para ver detecção em tempo real
2. Use opção 2 para calibrar as cores vermelhas
3. Ajuste os valores `RED_COLOR_LOWER` e `RED_COLOR_UPPER` no `config.py`

### Bot não clica corretamente
1. Verifique se a resolução do jogo está correta
2. Ajuste as coordenadas do minimapa em `MINIMAP_REGION`
3. Teste com diferentes configurações de janela (tela cheia vs janela)

## ⚙️ Configurações Avançadas

### Ajustar Detecção de Cores
```python
# Para diferentes condições de iluminação
RED_COLOR_LOWER = [0, 80, 80]      # Menos restritivo
RED_COLOR_UPPER = [15, 255, 255]   # Mais amplo

# Para detecção mais precisa
RED_COLOR_LOWER = [0, 150, 150]    # Mais restritivo
RED_COLOR_UPPER = [8, 255, 255]    # Mais específico
```

### Ajustar Comportamento
```python
# Timing
SCREENSHOT_INTERVAL = 0.3    # Mais rápido (padrão: 0.5)
ACTION_DELAY = 0.3          # Mais rápido (padrão: 0.5)

# Segurança
HEALTH_LOW_THRESHOLD = 30    # Recall mais cedo (padrão: 20)
HEALTH_CRITICAL_THRESHOLD = 15  # Emergência (padrão: 10)
```

## 📁 Estrutura dos Arquivos

```
Bot-Upp/
├── lol_bot_complete.py      # Bot principal (USE ESTE)
├── main.py                  # Bot antigo (sistema de decisão)
├── client_automation.py     # Automação do cliente
├── vision.py               # Sistema de visão/detecção
├── actions.py              # Ações do jogo (cliques, teclas)
├── config.py               # Configurações
├── test_color_detection.py # Teste e calibração
├── decision_engine.py      # Sistema de decisão avançado
└── requirements.txt        # Dependências
```

## 🔍 Debug e Logs

Para ver o que o bot está fazendo:

```python
# No config.py
DEBUG = True
```

Isso mostrará:
- Ações sendo executadas
- Inimigos detectados
- Coordenadas de clique
- Status do jogo

## ⚡ Dicas de Performance

1. **Use resolução 1920x1080** para melhor detecção
2. **Configure gráficos médios** para cores mais consistentes
3. **Desative sombras** se a detecção estiver inconsistente
4. **Use modo janela** para melhor controle
5. **Feche outros programas** para melhor performance

## 🚫 Limitações

- Funciona apenas no modo introdução contra bots
- Detecção baseada em cores pode falhar com diferentes configurações gráficas
- Não funciona com skins que alteram cores significativamente
- Requer calibração para diferentes resoluções/configurações

## 📞 Suporte

Se encontrar problemas:

1. Execute `test_color_detection.py` para diagnosticar
2. Verifique os logs no console
3. Ajuste as configurações no `config.py`
4. Teste com diferentes configurações gráficas do jogo

---

**Lembre-se: Use apenas para aprendizado e teste. O uso em contas principais pode resultar em banimento!**
