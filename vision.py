import cv2
import numpy as np
from PIL import Image
import config

class Vision:
    def __init__(self):
        # Load templates if needed
        self.health_template = None  # Load health bar template
        self.minion_template = None  # Load minion template

    def preprocess_image(self, img):
        """Preprocess image for better detection"""
        gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
        return gray

    def detect_health(self, img):
        """Detect player health percentage"""
        # Use configured health bar region
        x, y, w, h = config.HEALTH_BAR_REGION
        health_region = img[y:y+h, x:x+w]
        gray = cv2.cvtColor(health_region, cv2.COLOR_RGB2GRAY)
        # Simple method: count white pixels (assuming health is white)
        white_pixels = np.sum(gray > 200)
        total_pixels = gray.size
        health_percent = (white_pixels / total_pixels) * 100
        return min(100, max(0, health_percent))

    def detect_minions(self, img):
        """Detect positions of nearby minions"""
        # Use color detection for minions
        hsv = cv2.cvtColor(img, cv2.COLOR_RGB2HSV)
        # Use configured color ranges
        lower = np.array(config.MINION_COLOR_LOWER)
        upper = np.array(config.MINION_COLOR_UPPER)
        mask = cv2.inRange(hsv, lower, upper)
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        minions = []
        for cnt in contours:
            if cv2.contourArea(cnt) > 100:  # Filter small contours
                x, y, w, h = cv2.boundingRect(cnt)
                minions.append((x + w//2, y + h//2))  # Center position
        return minions

    def detect_towers(self, img):
        """Detect tower positions and health"""
        # Similar to minions, detect tower structures
        # Placeholder
        return []  # List of (x, y, health)

    def detect_abilities(self, img):
        """Detect ability cooldowns"""
        # Use configured ability bar region
        x, y, w, h = config.ABILITY_BAR_REGION
        ability_region = img[y:y+h, x:x+w]
        gray = cv2.cvtColor(ability_region, cv2.COLOR_RGB2GRAY)
        # Detect if abilities are available (not grayed out)
        # Placeholder: Assume all available
        return {'Q': True, 'W': True, 'E': True, 'R': False, 'Flash': True, 'Teleport': True}

    def detect_enemy_champion(self, img):
        """Detect if enemy champion is nearby"""
        # Use template matching or color detection
        # Placeholder
        return None  # Position or None