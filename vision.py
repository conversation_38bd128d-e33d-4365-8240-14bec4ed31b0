import cv2
import numpy as np
from PIL import Image
import config

class Vision:
    def __init__(self):
        # Load templates if needed
        self.health_template = None  # Load health bar template
        self.minion_template = None  # Load minion template

    def preprocess_image(self, img):
        """Preprocess image for better detection"""
        gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
        return gray

    def detect_health(self, img):
        """Detect player health percentage"""
        # Use configured health bar region
        x, y, w, h = config.HEALTH_BAR_REGION
        health_region = img[y:y+h, x:x+w]
        gray = cv2.cvtColor(health_region, cv2.COLOR_RGB2GRAY)
        # Simple method: count white pixels (assuming health is white)
        white_pixels = np.sum(gray > 200)
        total_pixels = gray.size
        health_percent = (white_pixels / total_pixels) * 100
        return min(100, max(0, health_percent))

    def detect_minions(self, img):
        """Detect positions of nearby minions (both ally and enemy)"""
        minions = []

        # Detect ally minions (blue)
        ally_minions = self.detect_by_color(img, config.MINION_COLOR_LOWER, config.MINION_COLOR_UPPER)
        minions.extend(ally_minions)

        # Detect enemy minions (red)
        enemy_minions = self.detect_red_enemies(img, min_area=100)
        minions.extend(enemy_minions)

        return minions

    def detect_red_enemies(self, img, min_area=50):
        """Detect enemies by red color (minions and champions)"""
        hsv = cv2.cvtColor(img, cv2.COLOR_RGB2HSV)

        # Create mask for red colors (HSV red wraps around)
        lower1 = np.array(config.RED_COLOR_LOWER)
        upper1 = np.array(config.RED_COLOR_UPPER)
        lower2 = np.array(config.RED_COLOR_LOWER2)
        upper2 = np.array(config.RED_COLOR_UPPER2)

        mask1 = cv2.inRange(hsv, lower1, upper1)
        mask2 = cv2.inRange(hsv, lower2, upper2)
        mask = cv2.bitwise_or(mask1, mask2)

        # Find contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        enemies = []

        for cnt in contours:
            if cv2.contourArea(cnt) > min_area:
                x, y, w, h = cv2.boundingRect(cnt)
                center_x, center_y = x + w//2, y + h//2
                enemies.append((center_x, center_y))

        return enemies

    def detect_by_color(self, img, color_lower, color_upper, min_area=100):
        """Generic color detection method"""
        hsv = cv2.cvtColor(img, cv2.COLOR_RGB2HSV)
        lower = np.array(color_lower)
        upper = np.array(color_upper)
        mask = cv2.inRange(hsv, lower, upper)

        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        objects = []

        for cnt in contours:
            if cv2.contourArea(cnt) > min_area:
                x, y, w, h = cv2.boundingRect(cnt)
                objects.append((x + w//2, y + h//2))

        return objects

    def detect_towers(self, img):
        """Detect tower positions and health"""
        # Similar to minions, detect tower structures
        # Placeholder
        return []  # List of (x, y, health)

    def detect_abilities(self, img):
        """Detect ability cooldowns"""
        # Use configured ability bar region
        x, y, w, h = config.ABILITY_BAR_REGION
        ability_region = img[y:y+h, x:x+w]
        gray = cv2.cvtColor(ability_region, cv2.COLOR_RGB2GRAY)
        # Detect if abilities are available (not grayed out)
        # Placeholder: Assume all available
        return {'Q': True, 'W': True, 'E': True, 'R': False, 'Flash': True, 'Teleport': True}

    def detect_enemy_champion(self, img):
        """Detect enemy champions by red health bars"""
        # Look for larger red objects that could be champions
        enemies = self.detect_red_enemies(img, min_area=200)  # Larger area for champions

        if enemies:
            # Return the closest enemy (first one found)
            return enemies[0]

        return None