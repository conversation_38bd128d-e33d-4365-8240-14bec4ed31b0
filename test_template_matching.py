"""
Teste e Calibração do Template Matching
Use este script para testar a detecção de minions pelas imagens
"""

import cv2
import numpy as np
import mss
import pyautogui
from PIL import Image
import time
from template_detection import TemplateDetector

def test_template_detection():
    """Testa a detecção de templates em tempo real"""
    print("🎯 TESTE DE TEMPLATE MATCHING")
    print("=" * 50)
    print("Controles:")
    print("- 'q': Sair")
    print("- 's': Salvar screenshot atual")
    print("- 'a': Mostrar/ocultar aliados")
    print("- 'e': Mostrar/ocultar inimigos")
    print("- '+': Aumentar threshold")
    print("- '-': Diminuir threshold")
    print()
    
    detector = TemplateDetector()
    sct = mss.mss()
    
    # Configurações
    show_allies = True
    show_enemies = True
    threshold = 0.7
    
    # Verifica se templates foram carregados
    if detector.minion_aliado_template is None:
        print("❌ Template de aliados não carregado!")
        return
    if detector.minion_inimigo_template is None:
        print("❌ Template de inimigos não carregado!")
        return
    
    print("✅ Templates carregados com sucesso!")
    print(f"📊 Threshold inicial: {threshold}")
    print()
    
    monitor = sct.monitors[1]  # Monitor principal
    
    while True:
        try:
            # Captura tela
            screenshot = sct.grab(monitor)
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            img_np = np.array(img)
            
            # Cria imagem de resultado
            result_img = img_np.copy()
            
            # Detecta aliados
            if show_allies:
                ally_detections = detector.detect_minions_by_template(
                    img_np, detector.minion_aliado_template, threshold
                )
                for x, y, conf in ally_detections:
                    cv2.circle(result_img, (x, y), 25, (255, 0, 0), 3)  # Azul
                    cv2.putText(result_img, f"Ally {conf:.2f}", (x-40, y-30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
            
            # Detecta inimigos
            if show_enemies:
                enemy_detections = detector.detect_minions_by_template(
                    img_np, detector.minion_inimigo_template, threshold
                )
                for x, y, conf in enemy_detections:
                    cv2.circle(result_img, (x, y), 25, (0, 0, 255), 3)  # Vermelho
                    cv2.putText(result_img, f"Enemy {conf:.2f}", (x-40, y-30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            
            # Informações na tela
            info_text = [
                f"Threshold: {threshold:.2f}",
                f"Aliados: {'ON' if show_allies else 'OFF'}",
                f"Inimigos: {'ON' if show_enemies else 'OFF'}",
                "Pressione 'q' para sair"
            ]
            
            for i, text in enumerate(info_text):
                cv2.putText(result_img, text, (10, 30 + i*25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # Redimensiona se muito grande
            height, width = result_img.shape[:2]
            if width > 1400:
                scale = 1400 / width
                new_width = int(width * scale)
                new_height = int(height * scale)
                result_img = cv2.resize(result_img, (new_width, new_height))
            
            # Mostra resultado
            cv2.imshow('Template Matching Test', result_img)
            
            # Controles
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                filename = f"template_test_{int(time.time())}.png"
                cv2.imwrite(filename, result_img)
                print(f"💾 Screenshot salvo: {filename}")
            elif key == ord('a'):
                show_allies = not show_allies
                print(f"👥 Aliados: {'ON' if show_allies else 'OFF'}")
            elif key == ord('e'):
                show_enemies = not show_enemies
                print(f"⚔️  Inimigos: {'ON' if show_enemies else 'OFF'}")
            elif key == ord('+') or key == ord('='):
                threshold = min(1.0, threshold + 0.05)
                print(f"📈 Threshold: {threshold:.2f}")
            elif key == ord('-'):
                threshold = max(0.1, threshold - 0.05)
                print(f"📉 Threshold: {threshold:.2f}")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ Erro: {e}")
            time.sleep(1)
    
    cv2.destroyAllWindows()
    print("✅ Teste finalizado")

def calibrate_templates():
    """Calibração interativa dos templates"""
    print("🔧 CALIBRAÇÃO DE TEMPLATES")
    print("=" * 50)
    
    detector = TemplateDetector()
    
    if detector.minion_aliado_template is None or detector.minion_inimigo_template is None:
        print("❌ Templates não carregados!")
        print("Certifique-se de que existem:")
        print("- img/minions_aliados.png")
        print("- img/minions_inimigos.png")
        return
    
    # Mostra informações dos templates
    print("📊 INFORMAÇÕES DOS TEMPLATES:")
    print(f"Aliados: {detector.minion_aliado_template.shape}")
    print(f"Inimigos: {detector.minion_inimigo_template.shape}")
    print()
    
    # Mostra os templates
    cv2.imshow('Template Aliados', detector.minion_aliado_template)
    cv2.imshow('Template Inimigos', detector.minion_inimigo_template)
    
    print("👀 Templates carregados nas janelas")
    print("Pressione qualquer tecla para continuar...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    # Inicia teste em tempo real
    test_template_detection()

def analyze_templates():
    """Analisa as imagens template"""
    print("🔍 ANÁLISE DE TEMPLATES")
    print("=" * 50)
    
    try:
        # Carrega templates
        ally_template = cv2.imread('img/minions_aliados.png', cv2.IMREAD_COLOR)
        enemy_template = cv2.imread('img/minions_inimigos.png', cv2.IMREAD_COLOR)
        
        if ally_template is None:
            print("❌ Não foi possível carregar img/minions_aliados.png")
            return
        if enemy_template is None:
            print("❌ Não foi possível carregar img/minions_inimigos.png")
            return
        
        print("✅ Templates carregados com sucesso!")
        print()
        
        # Análise dos templates
        print("📊 ANÁLISE DETALHADA:")
        print(f"Template Aliados:")
        print(f"  - Tamanho: {ally_template.shape}")
        print(f"  - Tipo: {ally_template.dtype}")
        print(f"  - Cor média: {np.mean(ally_template, axis=(0,1))}")
        print()
        
        print(f"Template Inimigos:")
        print(f"  - Tamanho: {enemy_template.shape}")
        print(f"  - Tipo: {enemy_template.dtype}")
        print(f"  - Cor média: {np.mean(enemy_template, axis=(0,1))}")
        print()
        
        # Converte para HSV para análise de cor
        ally_hsv = cv2.cvtColor(ally_template, cv2.COLOR_BGR2HSV)
        enemy_hsv = cv2.cvtColor(enemy_template, cv2.COLOR_BGR2HSV)
        
        print("🎨 ANÁLISE DE COR (HSV):")
        print(f"Aliados - Matiz média: {np.mean(ally_hsv[:,:,0]):.1f}")
        print(f"Inimigos - Matiz média: {np.mean(enemy_hsv[:,:,0]):.1f}")
        print()
        
        # Recomendações
        print("💡 RECOMENDAÇÕES:")
        if ally_template.shape[0] < 20 or ally_template.shape[1] < 20:
            print("⚠️  Template de aliados muito pequeno - considere usar imagem maior")
        if enemy_template.shape[0] < 20 or enemy_template.shape[1] < 20:
            print("⚠️  Template de inimigos muito pequeno - considere usar imagem maior")
        
        if ally_template.shape != enemy_template.shape:
            print("⚠️  Templates têm tamanhos diferentes - pode afetar detecção")
        
        print("✅ Use threshold entre 0.6-0.8 para começar")
        print("✅ Teste com diferentes condições de iluminação")
        
    except Exception as e:
        print(f"❌ Erro na análise: {e}")

def main():
    """Menu principal"""
    while True:
        print("\n" + "=" * 60)
        print("🎯 TESTE DE TEMPLATE MATCHING")
        print("=" * 60)
        print("1. 🔧 Calibrar templates")
        print("2. 🎮 Teste em tempo real")
        print("3. 🔍 Analisar templates")
        print("4. ❌ Sair")
        print("-" * 60)
        
        try:
            choice = input("Escolha uma opção (1-4): ").strip()
            
            if choice == "1":
                calibrate_templates()
            elif choice == "2":
                test_template_detection()
            elif choice == "3":
                analyze_templates()
            elif choice == "4":
                print("👋 Saindo...")
                break
            else:
                print("❌ Opção inválida!")
                
        except KeyboardInterrupt:
            print("\n👋 Saindo...")
            break
        except Exception as e:
            print(f"❌ Erro: {e}")

if __name__ == "__main__":
    main()
