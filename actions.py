import pyautogui
import time
import config

class Actions:
    def __init__(self, window):
        self.window = window
        # Set pyautogui region to game window
        if window:
            pyautogui.FAILSAFE = True
            pyautogui.PAUSE = 0.05
            # Note: pyautogui doesn't have region for all functions, so we adjust coordinates

    def _to_screen_coords(self, x, y):
        """Convert game-relative coordinates to screen coordinates"""
        if self.window:
            return self.window.left + x, self.window.top + y
        return x, y

    def move_mouse_to(self, x, y):
        """Move mouse to game coordinates"""
        screen_x, screen_y = self._to_screen_coords(x, y)
        pyautogui.moveTo(screen_x, screen_y)

    def click_at(self, x, y, button='left'):
        """Click at game coordinates"""
        screen_x, screen_y = self._to_screen_coords(x, y)
        pyautogui.click(screen_x, screen_y, button=button)

    def right_click_at(self, x, y):
        """Right click at game coordinates"""
        self.click_at(x, y, button='right')

    def press_key(self, key):
        """Press a key"""
        pyautogui.press(key)

    def move_character(self, direction):
        """Move character in direction"""
        if direction == 'forward':
            self.press_key(config.KEY_BINDINGS['Move Forward'])
        elif direction == 'backward':
            self.press_key(config.KEY_BINDINGS['Move Backward'])
        elif direction == 'left':
            self.press_key(config.KEY_BINDINGS['Move Left'])
        elif direction == 'right':
            self.press_key(config.KEY_BINDINGS['Move Right'])

    def attack(self):
        """Basic attack (left click)"""
        pyautogui.click()

    def attack_move(self, x, y):
        """Attack move to position (right click)"""
        screen_x, screen_y = self._to_screen_coords(x, y)
        pyautogui.click(screen_x, screen_y, button='right')

    def farm_minion(self, x, y):
        """Farm minion with right click"""
        self.attack_move(x, y)

    def attack_enemy(self, x, y):
        """Attack enemy with right click"""
        self.attack_move(x, y)

    def use_ability(self, ability):
        """Use ability by key"""
        if ability in config.KEY_BINDINGS:
            self.press_key(config.KEY_BINDINGS[ability])

    def recall(self):
        """Recall to base"""
        self.press_key(config.KEY_BINDINGS['Recall'])

    def buy_items(self):
        """Open shop and buy items (placeholder)"""
        self.press_key(config.KEY_BINDINGS['Shop'])
        time.sleep(0.5)
        # Simulate buying items
        # This would need more complex logic

    def navigate_to_lane(self, lane="bot"):
        """Navigate to lane using minimap"""
        if lane in config.LANE_POSITIONS:
            pos = config.LANE_POSITIONS[lane]
            self.click_at(pos[0], pos[1])
            time.sleep(0.5)
            return True
        return False

    def click_minimap(self, x, y):
        """Click on minimap coordinates"""
        # Minimap coordinates are relative to the minimap region
        minimap_x = config.MINIMAP_REGION[0] + x
        minimap_y = config.MINIMAP_REGION[1] + y
        screen_x, screen_y = self._to_screen_coords(minimap_x, minimap_y)
        pyautogui.click(screen_x, screen_y)

    def level_up_ability(self, ability):
        """Level up ability (placeholder)"""
        # In intro, abilities are auto-leveled or manual
        if ability in config.KEY_BINDINGS:
            # Hold Ctrl and press ability key to level up
            pyautogui.keyDown('ctrl')
            self.press_key(config.KEY_BINDINGS[ability])
            pyautogui.keyUp('ctrl')