# 📸 Guia do Bot Template Matching

## 🎯 O que é Template Matching?

O **Template Matching** é uma técnica que usa **suas próprias imagens** de minions como referência para detectá-los no jogo. É mais preciso que detecção por cor porque reconhece a forma e aparência exata dos minions.

## 🚀 Como Funciona

1. **Você fornece imagens** de minions aliados e inimigos
2. **O bot procura essas imagens** na tela em tempo real
3. **Quando encontra um inimigo**, clica com botão direito
4. **Segue o movimento** do alvo usando predição

## 📁 Preparação das Imagens

### 📸 Capturando as Imagens

1. **Entre em uma partida** do League of Legends
2. **Tire screenshots** quando vir minions claramente
3. **Recorte apenas o minion** (sem fundo desnecessário)
4. **Salve as imagens** na pasta `img/`:
   - `img/minions_aliados.png` - Minions azuis (seus)
   - `img/minions_inimigos.png` - Minions vermelhos (inimigos)

### 📏 Especificações das Imagens

- **Formato:** PNG (recomendado)
- **Tamanho:** 30x30 a 80x80 pixels
- **Qualidade:** Clara, sem blur
- **Fundo:** Mínimo possível
- **Ângulo:** Visão de cima (como no jogo)

### 💡 Dicas para Boas Imagens

- ✅ **Capture em resolução nativa** do jogo
- ✅ **Use configurações gráficas médias/altas**
- ✅ **Minion bem visível** e centralizado
- ✅ **Sem outros elementos** na imagem
- ❌ **Evite sombras** excessivas
- ❌ **Não inclua barras de vida** na imagem
- ❌ **Evite minions muito pequenos** ou distantes

## 🎮 Como Usar

### 1. **Preparação**
```bash
# Certifique-se de ter as imagens
ls img/
# Deve mostrar:
# minions_aliados.png
# minions_inimigos.png
```

### 2. **Teste as Imagens**
```bash
python start_bot.py
# Escolha opção 6 (Testar Template Matching)
```

### 3. **Execute o Bot**
```bash
python start_bot.py
# Escolha opção 4 (Bot Template Matching)
```

## 🔧 Calibração e Teste

### **Teste em Tempo Real**

```bash
python test_template_matching.py
```

**Controles durante o teste:**
- `q` - Sair
- `s` - Salvar screenshot
- `a` - Mostrar/ocultar aliados
- `e` - Mostrar/ocultar inimigos
- `+` - Aumentar precisão (threshold)
- `-` - Diminuir precisão (threshold)

### **Ajuste de Precisão (Threshold)**

- **0.9-1.0:** Muito preciso (pode perder detecções)
- **0.7-0.8:** Balanceado (recomendado)
- **0.5-0.6:** Menos preciso (mais detecções falsas)

## 🎯 Funcionalidades Avançadas

### **Seguimento de Alvos**
- ✅ **Rastreia movimento** de minions
- ✅ **Prediz posição futura** (0.3 segundos)
- ✅ **Clica onde o minion vai estar**
- ✅ **Mantém foco** no mesmo alvo

### **Debug Visual**
- 🔵 **Círculos azuis:** Minions aliados
- 🔴 **Círculos vermelhos:** Minions inimigos
- 🟡 **Círculos amarelos:** Posição predita
- 📊 **Números:** Confiança da detecção

### **Sistema Inteligente**
- 🎯 **Prioriza alvos próximos** ao centro da tela
- ⚡ **Usa habilidades** ocasionalmente
- 🗺️ **Navega para rota** automaticamente
- 🏠 **Recall** quando necessário

## 🛠️ Solução de Problemas

### **Bot não detecta minions**

1. **Teste suas imagens:**
   ```bash
   python test_template_matching.py
   ```

2. **Verifique se as imagens existem:**
   - `img/minions_aliados.png`
   - `img/minions_inimigos.png`

3. **Ajuste o threshold:**
   - Diminua para 0.6 se não detecta nada
   - Aumente para 0.8 se detecta coisas erradas

### **Muitas detecções falsas**

- ✅ **Aumente o threshold** para 0.8-0.9
- ✅ **Use imagens mais específicas**
- ✅ **Recorte melhor** as imagens
- ✅ **Evite fundos** nas imagens

### **Bot clica nos lugares errados**

- ✅ **Verifique resolução** do jogo
- ✅ **Use modo janela** sem bordas
- ✅ **Calibre coordenadas** do minimapa

### **Performance lenta**

- ✅ **Feche outros programas**
- ✅ **Use imagens menores** (40x40 pixels)
- ✅ **Diminua qualidade gráfica** do jogo

## 📊 Comparação com Outros Métodos

| Método | Precisão | Velocidade | Facilidade |
|--------|----------|------------|------------|
| **Template Matching** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| Detecção por Cor | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Bot Simples | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

## 🎯 Vantagens do Template Matching

- ✅ **Mais preciso** que detecção por cor
- ✅ **Reconhece formas** específicas
- ✅ **Menos falsos positivos**
- ✅ **Funciona com diferentes skins**
- ✅ **Segue movimento** dos alvos
- ✅ **Debug visual** em tempo real

## 📝 Exemplo de Uso Completo

```bash
# 1. Prepare as imagens
# Coloque minions_aliados.png e minions_inimigos.png na pasta img/

# 2. Teste as imagens
python test_template_matching.py
# Ajuste threshold até detectar bem

# 3. Execute o bot
python start_bot.py
# Escolha opção 4

# 4. Entre em uma partida
# O bot começará a farmar automaticamente!
```

## 🔄 Fluxo do Bot

1. **Captura tela** (10 FPS)
2. **Procura minions** usando suas imagens
3. **Seleciona melhor alvo** (mais próximo)
4. **Prediz movimento** do alvo
5. **Clica com botão direito** na posição predita
6. **Usa habilidades** ocasionalmente
7. **Repete o processo**

## 💡 Dicas Avançadas

### **Para Melhor Detecção:**
- Use **múltiplas imagens** do mesmo tipo de minion
- Capture em **diferentes ângulos**
- Teste com **diferentes iluminações**
- Use **configurações gráficas consistentes**

### **Para Melhor Performance:**
- **Imagens pequenas** (30-50 pixels)
- **Threshold otimizado** (0.7-0.8)
- **Feche programas** desnecessários
- **Use SSD** se possível

---

**🎯 O Template Matching é a opção mais avançada e precisa do bot!**
**Use suas próprias imagens para resultados perfeitos!**
