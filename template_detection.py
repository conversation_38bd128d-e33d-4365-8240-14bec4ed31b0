"""
Sistema de Detecção por Template Matching
Detecta minions aliados e inimigos usando imagens de referência
"""

import cv2
import numpy as np
import time
from typing import List, Tuple, Optional
import config

class TemplateDetector:
    def __init__(self):
        self.minion_aliado_template = None
        self.minion_inimigo_template = None
        self.last_enemy_positions = []  # Para rastreamento de movimento
        self.target_history = {}  # Histórico de alvos para seguimento
        self.load_templates()
    
    def load_templates(self):
        """Carrega as imagens template dos minions"""
        try:
            # Carrega template dos minions aliados (azuis)
            self.minion_aliado_template = cv2.imread('img/minions_aliados.png', cv2.IMREAD_COLOR)
            if self.minion_aliado_template is not None:
                print("✅ Template de minions aliados carregado")
                print(f"   Tamanho: {self.minion_aliado_template.shape}")
            else:
                print("❌ Erro ao carregar img/minions_aliados.png")
            
            # Carrega template dos minions inimigos (vermelhos)
            self.minion_inimigo_template = cv2.imread('img/minions_inimigos.png', cv2.IMREAD_COLOR)
            if self.minion_inimigo_template is not None:
                print("✅ Template de minions inimigos carregado")
                print(f"   Tamanho: {self.minion_inimigo_template.shape}")
            else:
                print("❌ Erro ao carregar img/minions_inimigos.png")
                
        except Exception as e:
            print(f"❌ Erro ao carregar templates: {e}")
    
    def detect_minions_by_template(self, screenshot, template, threshold=0.7, max_detections=10):
        """
        Detecta minions usando template matching
        
        Args:
            screenshot: Imagem da tela
            template: Template do minion
            threshold: Limiar de confiança (0.0 a 1.0)
            max_detections: Máximo de detecções
        
        Returns:
            Lista de posições (x, y, confidence)
        """
        if template is None:
            return []
        
        try:
            # Converte para escala de cinza para melhor matching
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_RGB2GRAY)
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            
            # Template matching
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            
            # Encontra todas as correspondências acima do threshold
            locations = np.where(result >= threshold)
            
            detections = []
            template_h, template_w = template_gray.shape
            
            # Converte localizações para lista de detecções
            for pt in zip(*locations[::-1]):  # Switch x and y
                x, y = pt
                confidence = result[y, x]
                
                # Centro do template
                center_x = x + template_w // 2
                center_y = y + template_h // 2
                
                detections.append((center_x, center_y, confidence))
            
            # Remove detecções muito próximas (non-maximum suppression simples)
            filtered_detections = self.filter_close_detections(detections, min_distance=50)
            
            # Limita o número de detecções
            return filtered_detections[:max_detections]
            
        except Exception as e:
            print(f"❌ Erro na detecção por template: {e}")
            return []
    
    def filter_close_detections(self, detections, min_distance=50):
        """Remove detecções muito próximas umas das outras"""
        if not detections:
            return []
        
        # Ordena por confiança (maior primeiro)
        detections.sort(key=lambda x: x[2], reverse=True)
        
        filtered = []
        for detection in detections:
            x, y, conf = detection
            
            # Verifica se está muito próximo de alguma detecção já aceita
            too_close = False
            for fx, fy, _ in filtered:
                distance = np.sqrt((x - fx)**2 + (y - fy)**2)
                if distance < min_distance:
                    too_close = True
                    break
            
            if not too_close:
                filtered.append(detection)
        
        return filtered
    
    def detect_ally_minions(self, screenshot, threshold=0.7):
        """Detecta minions aliados (azuis)"""
        return self.detect_minions_by_template(screenshot, self.minion_aliado_template, threshold)
    
    def detect_enemy_minions(self, screenshot, threshold=0.7):
        """Detecta minions inimigos (vermelhos)"""
        detections = self.detect_minions_by_template(screenshot, self.minion_inimigo_template, threshold)
        
        # Atualiza histórico de posições para rastreamento
        current_time = time.time()
        current_positions = [(x, y) for x, y, _ in detections]
        
        # Atualiza rastreamento de movimento
        self.update_target_tracking(current_positions, current_time)
        
        return detections
    
    def update_target_tracking(self, current_positions, current_time):
        """Atualiza o rastreamento de alvos em movimento"""
        # Remove alvos antigos (mais de 5 segundos)
        old_targets = []
        for target_id, data in self.target_history.items():
            if current_time - data['last_seen'] > 5.0:
                old_targets.append(target_id)
        
        for target_id in old_targets:
            del self.target_history[target_id]
        
        # Associa posições atuais com alvos existentes ou cria novos
        for pos in current_positions:
            x, y = pos
            
            # Procura alvo existente próximo
            closest_target = None
            min_distance = float('inf')
            
            for target_id, data in self.target_history.items():
                last_x, last_y = data['position']
                distance = np.sqrt((x - last_x)**2 + (y - last_y)**2)
                
                if distance < 100 and distance < min_distance:  # 100 pixels de tolerância
                    min_distance = distance
                    closest_target = target_id
            
            if closest_target:
                # Atualiza alvo existente
                self.target_history[closest_target].update({
                    'position': (x, y),
                    'last_seen': current_time,
                    'velocity': self.calculate_velocity(closest_target, x, y, current_time)
                })
            else:
                # Cria novo alvo
                new_id = f"target_{len(self.target_history)}_{int(current_time)}"
                self.target_history[new_id] = {
                    'position': (x, y),
                    'last_seen': current_time,
                    'velocity': (0, 0),
                    'created': current_time
                }
    
    def calculate_velocity(self, target_id, new_x, new_y, current_time):
        """Calcula a velocidade de movimento de um alvo"""
        if target_id not in self.target_history:
            return (0, 0)
        
        data = self.target_history[target_id]
        old_x, old_y = data['position']
        old_time = data['last_seen']
        
        time_diff = current_time - old_time
        if time_diff <= 0:
            return (0, 0)
        
        vel_x = (new_x - old_x) / time_diff
        vel_y = (new_y - old_y) / time_diff
        
        return (vel_x, vel_y)
    
    def predict_target_position(self, target_id, time_ahead=0.5):
        """Prediz a posição futura de um alvo baseado na velocidade"""
        if target_id not in self.target_history:
            return None
        
        data = self.target_history[target_id]
        x, y = data['position']
        vel_x, vel_y = data['velocity']
        
        # Prediz posição futura
        future_x = x + vel_x * time_ahead
        future_y = y + vel_y * time_ahead
        
        return (int(future_x), int(future_y))
    
    def get_best_enemy_target(self, screenshot):
        """
        Retorna o melhor alvo inimigo para atacar
        Considera proximidade, movimento e confiança da detecção
        """
        enemy_detections = self.detect_enemy_minions(screenshot)
        
        if not enemy_detections:
            return None
        
        # Calcula centro da tela para priorizar alvos próximos
        screen_h, screen_w = screenshot.shape[:2]
        screen_center_x, screen_center_y = screen_w // 2, screen_h // 2
        
        best_target = None
        best_score = -1
        
        for x, y, confidence in enemy_detections:
            # Calcula distância do centro da tela
            distance_from_center = np.sqrt((x - screen_center_x)**2 + (y - screen_center_y)**2)
            
            # Normaliza distância (0 = centro, 1 = borda)
            max_distance = np.sqrt(screen_w**2 + screen_h**2) / 2
            normalized_distance = distance_from_center / max_distance
            
            # Calcula score (maior = melhor)
            # Prioriza: alta confiança, proximidade do centro
            score = confidence * 0.7 + (1 - normalized_distance) * 0.3
            
            if score > best_score:
                best_score = score
                best_target = (x, y, confidence)
        
        return best_target
    
    def draw_detections(self, screenshot, show_allies=True, show_enemies=True):
        """
        Desenha as detecções na imagem para debug
        Retorna imagem com as detecções marcadas
        """
        result_img = screenshot.copy()
        
        if show_allies:
            ally_detections = self.detect_ally_minions(screenshot)
            for x, y, conf in ally_detections:
                cv2.circle(result_img, (x, y), 20, (255, 0, 0), 2)  # Azul para aliados
                cv2.putText(result_img, f"Ally {conf:.2f}", (x-30, y-25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        if show_enemies:
            enemy_detections = self.detect_enemy_minions(screenshot)
            for x, y, conf in enemy_detections:
                cv2.circle(result_img, (x, y), 20, (0, 0, 255), 2)  # Vermelho para inimigos
                cv2.putText(result_img, f"Enemy {conf:.2f}", (x-30, y-25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
                
                # Desenha predição de movimento
                for target_id, data in self.target_history.items():
                    tx, ty = data['position']
                    if abs(tx - x) < 50 and abs(ty - y) < 50:  # Se é o mesmo alvo
                        predicted_pos = self.predict_target_position(target_id)
                        if predicted_pos:
                            px, py = predicted_pos
                            cv2.circle(result_img, (px, py), 10, (0, 255, 255), 2)  # Amarelo para predição
                            cv2.line(result_img, (x, y), (px, py), (0, 255, 255), 1)
        
        return result_img
