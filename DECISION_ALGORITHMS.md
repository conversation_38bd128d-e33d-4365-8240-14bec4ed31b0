# Optimal Decision-Making Algorithms for Bot Actions

## Overview

This document explains the optimal decision-making algorithms for prioritizing actions like farming, fighting, and recalling in the League of Legends bot.

## Current Implementation

The bot now supports two decision-making approaches:

1. **Simple Priority System** (`make_decision` method)
2. **Advanced Multi-Factor Scoring** (`DecisionEngine` class)

## Algorithm Types

### 1. Simple Priority Hierarchy (Basic)

The basic algorithm uses a simple if-else chain:

```
Priority Order:
1. Recall (if health < threshold)
2. Fight (if enemy present)
3. Farm (if minions present)
4. Move to lane (default)
```

**Pros:**
- Fast execution
- Easy to understand
- Predictable behavior

**Cons:**
- Doesn't consider multiple factors
- Can make suboptimal decisions
- No risk assessment

### 2. Advanced Multi-Factor Scoring (Optimal)

The advanced algorithm uses weighted utility scoring:

```
For each action:
  Score = Σ(Factor_i × Weight_i)
  
Select action with highest score
```

## Key Decision Factors

### Safety Factors (Weight: 10)
- **Health percentage**: Lower health increases recall utility
- **Enemy proximity**: Reduces risky action utilities
- **Escape abilities**: Flash/mobility spells availability

### Economic Factors (Weight: 7)
- **Minion count**: More minions = higher farming utility
- **Gold efficiency**: Recall when enough gold for items
- **Opportunity cost**: Missing farm vs. potential kills

### Combat Factors (Weight: 8)
- **Health advantage**: Higher health = higher combat utility
- **Ability availability**: More abilities = higher combat utility
- **Level difference**: Higher level = combat advantage
- **Item power spikes**: Combat utility varies with items

### Positioning Factors (Weight: 5)
- **Map position**: Lane position affects action priorities
- **Ward coverage**: Vision affects risk assessment
- **Jungle presence**: Enemy jungler affects safety

### Temporal Factors
- **Action cooldowns**: Prevent action spam
- **Recent history**: Avoid repetitive patterns
- **Game phase**: Early/mid/late game priorities

## Utility Calculation Examples

### Recall Utility
```python
recall_utility = (
    (100 - health) * SAFETY_WEIGHT +
    gold_efficiency_bonus +
    mana_factor * RESOURCE_WEIGHT -
    enemy_proximity_penalty
)
```

### Combat Utility
```python
combat_utility = (
    base_combat_value +
    health_advantage * HEALTH_WEIGHT +
    ability_count * ABILITY_WEIGHT +
    level_advantage * LEVEL_WEIGHT
)
```

### Farming Utility
```python
farming_utility = (
    base_farming_value +
    minion_count * MINION_WEIGHT +
    safety_bonus -
    enemy_risk_penalty
)
```

## Risk Assessment

The algorithm applies risk assessment modifiers:

1. **Health-based risk**: Low health reduces aggressive actions
2. **Ability-based risk**: No escape abilities reduce combat utility
3. **Position-based risk**: Overextended position increases retreat utility
4. **Information-based risk**: No vision increases caution

## Temporal Constraints

To prevent erratic behavior:

1. **Action cooldowns**: Minimum time between same actions
2. **State persistence**: Don't change actions too frequently
3. **Commitment**: Complete started actions when safe

## Configuration

Key parameters in `config.py`:

```python
# Health thresholds
HEALTH_LOW_THRESHOLD = 20
HEALTH_CRITICAL_THRESHOLD = 10
HEALTH_SAFE_THRESHOLD = 60

# Decision weights
WEIGHTS = {
    'SAFETY': 10,
    'COMBAT': 8,
    'FARMING': 7,
    'POSITIONING': 5,
    'OBJECTIVES': 9
}

# Combat requirements
MIN_HEALTH_FOR_COMBAT = 30
MIN_ABILITIES_FOR_COMBAT = 2
```

## Usage

Enable advanced decision-making in `config.py`:

```python
USE_ADVANCED_DECISION_ENGINE = True
```

## Future Improvements

### Machine Learning Integration
- **Q-Learning**: Learn optimal actions from experience
- **Neural Networks**: Pattern recognition for complex situations
- **Reinforcement Learning**: Adapt to different opponents

### Advanced Features
- **Predictive modeling**: Anticipate enemy actions
- **Team coordination**: Multi-bot cooperation
- **Objective prioritization**: Dragon/Baron timing
- **Champion-specific logic**: Different strategies per champion

### Performance Optimization
- **Caching**: Store computed utilities
- **Parallel processing**: Evaluate actions simultaneously
- **Adaptive thresholds**: Adjust based on game state

## Debugging

Enable debug mode to see decision scores:

```python
DEBUG = True
```

This will print:
- Action scores for each decision
- Selected action and reasoning
- Game state information

## Best Practices

1. **Tune weights** based on champion and role
2. **Test thoroughly** in different game scenarios
3. **Monitor performance** and adjust thresholds
4. **Consider game phase** when setting priorities
5. **Balance safety vs. aggression** based on skill level

## Conclusion

The optimal decision-making algorithm combines multiple factors with appropriate weights, applies risk assessment, and considers temporal constraints. The advanced system provides much better decision-making than simple priority hierarchies while remaining configurable and debuggable.
