"""
Script de Diagnóstico para o Bot League of Legends
Use este script para identificar problemas de detecção de janela
"""

import pyautogui
import time
import sys

def listar_todas_janelas():
    """Lista todas as janelas abertas no sistema"""
    print("=" * 60)
    print("🔍 LISTANDO TODAS AS JANELAS ABERTAS")
    print("=" * 60)
    
    try:
        all_windows = pyautogui.getAllWindows()
        print(f"Total de janelas encontradas: {len(all_windows)}")
        print()
        
        # Filtra e mostra janelas com título
        windows_with_title = []
        for window in all_windows:
            if window.title.strip():  # Só janelas com título não vazio
                windows_with_title.append(window)
        
        print(f"Janelas com título: {len(windows_with_title)}")
        print("-" * 60)
        
        for i, window in enumerate(windows_with_title, 1):
            print(f"{i:2d}. Título: '{window.title}'")
            print(f"    Tamanho: {window.width}x{window.height}")
            print(f"    Posição: ({window.left}, {window.top})")
            
            # Verifica se pode ser o League of Legends
            title_lower = window.title.lower()
            if any(keyword in title_lower for keyword in ['league', 'riot', 'lol']):
                print(f"    ⭐ POSSÍVEL CLIENTE DO LOL!")
            
            print()
        
        return windows_with_title
        
    except Exception as e:
        print(f"❌ Erro ao listar janelas: {e}")
        return []

def testar_deteccao_lol():
    """Testa especificamente a detecção do League of Legends"""
    print("=" * 60)
    print("🎮 TESTANDO DETECÇÃO DO LEAGUE OF LEGENDS")
    print("=" * 60)
    
    # Títulos possíveis para testar
    titulos_teste = [
        "League of Legends",
        "Riot Client",
        "League of Legends (TM) Client",
        "LoL",
        "Riot Games",
        "League of Legends Client"
    ]
    
    encontrou_algum = False
    
    for titulo in titulos_teste:
        print(f"Testando título: '{titulo}'")
        try:
            windows = pyautogui.getWindowsWithTitle(titulo)
            if windows:
                print(f"  ✅ Encontrou {len(windows)} janela(s)!")
                for i, window in enumerate(windows):
                    print(f"    Janela {i+1}: {window.width}x{window.height} em ({window.left}, {window.top})")
                encontrou_algum = True
            else:
                print(f"  ❌ Nenhuma janela encontrada")
        except Exception as e:
            print(f"  ❌ Erro: {e}")
        print()
    
    if not encontrou_algum:
        print("⚠️  Nenhuma janela do LoL encontrada pelos títulos padrão")
        print("Procurando por palavras-chave...")
        
        try:
            all_windows = pyautogui.getAllWindows()
            for window in all_windows:
                title_lower = window.title.lower()
                if any(keyword in title_lower for keyword in ['league', 'riot', 'lol']):
                    print(f"  🔍 Encontrou: '{window.title}' - {window.width}x{window.height}")
                    encontrou_algum = True
        except Exception as e:
            print(f"❌ Erro na busca por palavras-chave: {e}")
    
    return encontrou_algum

def testar_ativacao_janela():
    """Testa a ativação de janelas"""
    print("=" * 60)
    print("🖱️  TESTANDO ATIVAÇÃO DE JANELA")
    print("=" * 60)
    
    try:
        # Procura janelas do LoL
        possible_titles = ["League of Legends", "Riot Client"]
        
        for title in possible_titles:
            windows = pyautogui.getWindowsWithTitle(title)
            if windows:
                window = windows[0]
                print(f"Tentando ativar: '{window.title}'")
                
                try:
                    window.activate()
                    print("  ✅ Ativação bem-sucedida!")
                    time.sleep(2)
                    return True
                except Exception as e:
                    print(f"  ❌ Erro na ativação: {e}")
        
        print("❌ Nenhuma janela do LoL encontrada para testar ativação")
        return False
        
    except Exception as e:
        print(f"❌ Erro geral no teste de ativação: {e}")
        return False

def verificar_dependencias():
    """Verifica se todas as dependências estão instaladas"""
    print("=" * 60)
    print("📦 VERIFICANDO DEPENDÊNCIAS")
    print("=" * 60)
    
    dependencias = [
        ('pyautogui', 'pyautogui'),
        ('opencv-python', 'cv2'),
        ('Pillow', 'PIL'),
        ('numpy', 'numpy'),
        ('mss', 'mss')
    ]
    
    todas_ok = True
    
    for nome_pacote, nome_import in dependencias:
        try:
            __import__(nome_import)
            print(f"✅ {nome_pacote}: OK")
        except ImportError:
            print(f"❌ {nome_pacote}: NÃO INSTALADO")
            todas_ok = False
    
    print()
    if todas_ok:
        print("✅ Todas as dependências estão instaladas!")
    else:
        print("❌ Algumas dependências estão faltando.")
        print("Execute: pip install -r requirements.txt")
    
    return todas_ok

def menu_interativo():
    """Menu interativo para diagnóstico"""
    while True:
        print("\n" + "=" * 60)
        print("🔧 DIAGNÓSTICO DO BOT LEAGUE OF LEGENDS")
        print("=" * 60)
        print("1. 📋 Listar todas as janelas abertas")
        print("2. 🎮 Testar detecção do League of Legends")
        print("3. 🖱️  Testar ativação de janela")
        print("4. 📦 Verificar dependências")
        print("5. 🔄 Executar diagnóstico completo")
        print("6. ❌ Sair")
        print("-" * 60)
        
        try:
            escolha = input("Digite sua escolha (1-6): ").strip()
            
            if escolha == "1":
                listar_todas_janelas()
                
            elif escolha == "2":
                testar_deteccao_lol()
                
            elif escolha == "3":
                testar_ativacao_janela()
                
            elif escolha == "4":
                verificar_dependencias()
                
            elif escolha == "5":
                print("\n🔄 EXECUTANDO DIAGNÓSTICO COMPLETO...")
                verificar_dependencias()
                print()
                testar_deteccao_lol()
                print()
                listar_todas_janelas()
                
            elif escolha == "6":
                print("👋 Saindo do diagnóstico...")
                break
                
            else:
                print("❌ Opção inválida! Digite um número de 1 a 6.")
                
        except KeyboardInterrupt:
            print("\n👋 Diagnóstico interrompido pelo usuário")
            break
        except Exception as e:
            print(f"❌ Erro inesperado: {e}")
        
        input("\nPressione Enter para continuar...")

def main():
    """Função principal"""
    print("🔧 DIAGNÓSTICO DO BOT LEAGUE OF LEGENDS")
    print("Este script ajuda a identificar problemas de detecção de janela")
    print()
    
    # Verifica se o LoL está rodando
    print("⚠️  ANTES DE CONTINUAR:")
    print("1. Certifique-se de que o League of Legends está aberto")
    print("2. O cliente deve estar visível na tela")
    print("3. Não minimize o cliente durante o diagnóstico")
    print()
    
    resposta = input("O League of Legends está aberto? (s/n): ").lower().strip()
    if resposta not in ['s', 'sim', 'y', 'yes']:
        print("❌ Abra o League of Legends primeiro e execute este script novamente.")
        return
    
    menu_interativo()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Programa encerrado pelo usuário")
    except Exception as e:
        print(f"❌ Erro fatal: {e}")
        input("Pressione Enter para sair...")
