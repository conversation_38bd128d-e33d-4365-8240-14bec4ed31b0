"""
Complete League of Legends Bot
Handles client automation, champion selection, and in-game farming
"""

import pyautogui
import cv2
import numpy as np
from PIL import Image
import mss
import time
import sys
import os
import random

from vision import Vision
from actions import Actions
from client_automation import ClientAutomation
import config

class CompleteLolBot:
    def __init__(self):
        self.game_window = None
        self.sct = mss.mss()
        self.vision = Vision()
        self.actions = None
        self.client_automation = ClientAutomation()
        self.game_started = False
        self.in_lane = False
        
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1
        
    def find_game_window(self):
        """Find the League of Legends game window"""
        try:
            windows = pyautogui.getWindowsWithTitle(config.GAME_WINDOW_TITLE)
            if windows:
                # Look for the largest window (game window vs client)
                largest_window = max(windows, key=lambda w: w.width * w.height)
                self.game_window = largest_window
                self.game_window.activate()
                self.actions = Actions(self.game_window)
                print(f"Found game window: {self.game_window.width}x{self.game_window.height}")
                return True
            return False
        except Exception as e:
            print(f"Error finding game window: {e}")
            return False
    
    def take_screenshot(self):
        """Take screenshot of game area"""
        if self.game_window:
            bbox = (self.game_window.left, self.game_window.top,
                   self.game_window.left + self.game_window.width,
                   self.game_window.top + self.game_window.height)
            screenshot = self.sct.grab(bbox)
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            return np.array(img)
        return None
    
    def is_in_game(self):
        """Check if we're currently in the game (not client)"""
        img = self.take_screenshot()
        if img is None:
            return False
        
        height, width = img.shape[:2]
        
        # Check for game UI elements
        if width > 1200 and height > 800:
            # Look for minimap region
            minimap_region = img[600:800, 1000:1200] if width > 1200 and height > 800 else None
            if minimap_region is not None:
                # Check for typical game colors in minimap
                avg_brightness = np.mean(minimap_region)
                return 50 < avg_brightness < 150  # Game minimap has moderate brightness
        
        return False
    
    def wait_for_game_start(self):
        """Wait for the game to start after client automation"""
        print("Waiting for game to start...")
        max_wait = 180  # 3 minutes
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            if self.find_game_window() and self.is_in_game():
                print("Game detected! Waiting for full load...")
                time.sleep(10)  # Wait for game to fully load
                self.game_started = True
                return True
            time.sleep(3)
        
        print("Timeout waiting for game to start")
        return False
    
    def navigate_to_lane(self):
        """Navigate to the selected lane"""
        if not self.in_lane:
            print(f"Navigating to {config.PREFERRED_LANE} lane...")
            
            # Click on minimap to go to lane
            if self.actions.navigate_to_lane(config.PREFERRED_LANE):
                time.sleep(3)
                self.in_lane = True
                print(f"Navigated to {config.PREFERRED_LANE} lane")
                return True
        
        return self.in_lane
    
    def farm_minions(self, img):
        """Farm nearby minions, prioritizing enemy minions (red)"""
        # Get all red enemies (minions and champions)
        red_enemies = self.vision.detect_red_enemies(img, min_area=30)

        if red_enemies:
            # Filter out champions (larger red areas) and keep only minions
            minion_enemies = []
            champion_enemies = []

            for enemy in red_enemies:
                # Simple heuristic: check area around the detected point
                x, y = enemy
                # Sample area around the point to estimate size
                sample_size = 20
                x1, y1 = max(0, x-sample_size), max(0, y-sample_size)
                x2, y2 = min(img.shape[1], x+sample_size), min(img.shape[0], y+sample_size)

                area_sample = img[y1:y2, x1:x2]
                if area_sample.size > 0:
                    red_pixels = self.count_red_pixels(area_sample)

                    # Champions usually have larger red health bars
                    if red_pixels > 100:  # Likely a champion
                        champion_enemies.append(enemy)
                    else:  # Likely a minion
                        minion_enemies.append(enemy)
                else:
                    minion_enemies.append(enemy)  # Default to minion

            # Prioritize minions for farming
            targets = minion_enemies if minion_enemies else red_enemies

            if targets:
                # Find closest target
                screen_center_x, screen_center_y = img.shape[1]//2, img.shape[0]//2
                closest_target = min(targets, key=lambda pos:
                                   (pos[0] - screen_center_x)**2 + (pos[1] - screen_center_y)**2)

                if config.DEBUG:
                    print(f"Farming red enemy at {closest_target}")

                self.actions.farm_minion(closest_target[0], closest_target[1])
                return True

        return False

    def count_red_pixels(self, img_region):
        """Count red pixels in an image region"""
        hsv = cv2.cvtColor(img_region, cv2.COLOR_RGB2HSV)

        # Create red mask
        lower1 = np.array(config.RED_COLOR_LOWER)
        upper1 = np.array(config.RED_COLOR_UPPER)
        lower2 = np.array(config.RED_COLOR_LOWER2)
        upper2 = np.array(config.RED_COLOR_UPPER2)

        mask1 = cv2.inRange(hsv, lower1, upper1)
        mask2 = cv2.inRange(hsv, lower2, upper2)
        mask = cv2.bitwise_or(mask1, mask2)

        return np.sum(mask > 0)
    
    def attack_enemies(self, img):
        """Attack enemy champions"""
        enemy = self.vision.detect_enemy_champion(img)
        
        if enemy:
            print(f"Attacking enemy champion at {enemy}")
            self.actions.attack_enemy(enemy[0], enemy[1])
            
            # Use abilities if available
            abilities = ['Q', 'W', 'E']
            random_ability = random.choice(abilities)
            self.actions.use_ability(random_ability)
            
            return True
        
        return False
    
    def game_loop(self):
        """Main game loop for farming and fighting"""
        print("Starting game loop...")
        
        while True:
            try:
                img = self.take_screenshot()
                if img is None:
                    print("Failed to take screenshot")
                    time.sleep(1)
                    continue
                
                # Check health for safety
                health = self.vision.detect_health(img)
                
                if health < config.HEALTH_CRITICAL_THRESHOLD:
                    print("Critical health! Recalling...")
                    self.actions.recall()
                    time.sleep(8)  # Wait for recall
                    continue
                
                # Navigate to lane if not there yet
                if not self.navigate_to_lane():
                    time.sleep(2)
                    continue
                
                # Priority 1: Attack enemy champions
                if self.attack_enemies(img):
                    time.sleep(1)
                    continue
                
                # Priority 2: Farm minions
                if self.farm_minions(img):
                    time.sleep(0.5)
                    continue
                
                # Priority 3: Move towards lane center if nothing to do
                if random.random() < 0.1:  # 10% chance to move randomly
                    lane_pos = config.LANE_POSITIONS[config.PREFERRED_LANE]
                    self.actions.click_at(lane_pos[0] + random.randint(-50, 50), 
                                        lane_pos[1] + random.randint(-50, 50))
                
                time.sleep(config.SCREENSHOT_INTERVAL)
                
            except KeyboardInterrupt:
                print("Bot stopped by user")
                break
            except Exception as e:
                print(f"Error in game loop: {e}")
                time.sleep(2)
    
    def run_complete_automation(self):
        """Run the complete automation sequence"""
        print("=== League of Legends Complete Bot ===")
        print("WARNING: This bot is for educational purposes only!")
        print("Use at your own risk - may violate Terms of Service")
        
        # Step 1: Client automation
        print("\n--- Step 1: Client Automation ---")
        if config.AUTO_START_GAME:
            if not self.client_automation.full_automation_sequence():
                print("Client automation failed")
                return False
        
        # Step 2: Wait for game to start
        print("\n--- Step 2: Waiting for Game ---")
        if not self.wait_for_game_start():
            print("Failed to detect game start")
            return False
        
        # Step 3: In-game automation
        print("\n--- Step 3: In-Game Automation ---")
        self.game_loop()
        
        return True
    
    def run_game_only(self):
        """Run only the in-game part (skip client automation)"""
        print("=== League of Legends Game Bot ===")
        print("Make sure you're already in a game!")
        
        if not self.find_game_window():
            print("Game window not found. Make sure you're in a League of Legends game.")
            return False
        
        if not self.is_in_game():
            print("Not detected as being in game. Make sure the game has started.")
            return False
        
        self.game_started = True
        self.game_loop()
        return True

def main():
    """Main function"""
    bot = CompleteLolBot()
    
    # Choose mode
    print("Choose mode:")
    print("1. Complete automation (client + game)")
    print("2. Game only (skip client automation)")
    
    try:
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            bot.run_complete_automation()
        elif choice == "2":
            bot.run_game_only()
        else:
            print("Invalid choice")
    except KeyboardInterrupt:
        print("\nBot stopped")

if __name__ == "__main__":
    main()
