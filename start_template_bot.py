#!/usr/bin/env python3
"""
Inicialização Rápida do Bot Template Matching
Use este script para iniciar diretamente o bot com suas imagens
"""

import os
import sys

def verificar_imagens():
    """Verifica se as imagens necessárias existem"""
    imagens_necessarias = [
        'img/minions_aliados.png',
        'img/minions_inimigos.png'
    ]
    
    imagens_faltando = []
    for img in imagens_necessarias:
        if not os.path.exists(img):
            imagens_faltando.append(img)
    
    return imagens_faltando

def main():
    print("=" * 60)
    print("📸 BOT TEMPLATE MATCHING - INICIALIZAÇÃO RÁPIDA")
    print("=" * 60)
    print()
    print("🎯 Este bot usa SUAS IMAGENS de minions para detecção!")
    print("✅ Mais preciso que detecção por cor")
    print("✅ Segue alvos em movimento")
    print("✅ Predição de posição futura")
    print("✅ Debug visual em tempo real")
    print()
    
    # Verifica imagens
    print("🔍 Verificando imagens necessárias...")
    imagens_faltando = verificar_imagens()
    
    if imagens_faltando:
        print("❌ IMAGENS FALTANDO:")
        for img in imagens_faltando:
            print(f"   - {img}")
        print()
        print("📋 COMO RESOLVER:")
        print("1. Entre em uma partida do League of Legends")
        print("2. Tire screenshots dos minions")
        print("3. Recorte apenas o minion (30x30 a 80x80 pixels)")
        print("4. Salve como:")
        print("   - img/minions_aliados.png (minions azuis)")
        print("   - img/minions_inimigos.png (minions vermelhos)")
        print()
        print("📖 Leia o GUIA_TEMPLATE_MATCHING.md para mais detalhes")
        print()
        
        resposta = input("Deseja continuar mesmo assim? (s/n): ").lower().strip()
        if resposta not in ['s', 'sim', 'y', 'yes']:
            print("👋 Prepare as imagens e execute novamente!")
            return
    else:
        print("✅ Todas as imagens encontradas!")
        print("   - img/minions_aliados.png ✓")
        print("   - img/minions_inimigos.png ✓")
    
    print()
    print("📋 INSTRUÇÕES:")
    print("1. Certifique-se de estar EM UMA PARTIDA (não no cliente)")
    print("2. O bot abrirá uma janela de debug mostrando detecções")
    print("3. Círculos azuis = minions aliados")
    print("4. Círculos vermelhos = minions inimigos")
    print("5. Círculos amarelos = predição de movimento")
    print("6. Pressione Ctrl+C para parar o bot")
    print()
    
    resposta = input("Está em uma partida e pronto para começar? (s/n): ").lower().strip()
    if resposta not in ['s', 'sim', 'y', 'yes']:
        print("👋 Entre em uma partida e execute novamente!")
        return
    
    print()
    print("🚀 INICIANDO BOT TEMPLATE MATCHING...")
    print("⏳ Aguarde alguns segundos para inicialização...")
    print()
    
    try:
        from bot_template_matching import BotTemplateMatching
        bot = BotTemplateMatching()
        bot.executar()
    except KeyboardInterrupt:
        print("\n⏹️  Bot parado pelo usuário")
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        print("Certifique-se de que todos os arquivos estão na mesma pasta")
    except Exception as e:
        print(f"❌ Erro ao executar bot: {e}")
        print("Execute 'python diagnostico.py' para mais informações")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Programa encerrado pelo usuário")
    except Exception as e:
        print(f"❌ Erro fatal: {e}")
        input("Pressione Enter para sair...")
