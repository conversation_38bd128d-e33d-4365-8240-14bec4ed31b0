"""
Advanced Decision Engine for League of Legends Bot
Implements sophisticated action prioritization algorithms
"""

import time
import math
from typing import Dict, List, Tuple, Optional
import config

class GameState:
    """Represents the current game state"""
    def __init__(self, health: float, mana: float, minions: List[Tuple], 
                 enemy: Optional[Tuple], abilities: Dict, gold: int = 0,
                 level: int = 1, items: List = None):
        self.health = health
        self.mana = mana
        self.minions = minions or []
        self.enemy = enemy
        self.abilities = abilities or {}
        self.gold = gold
        self.level = level
        self.items = items or []
        self.timestamp = time.time()

class DecisionEngine:
    """Advanced decision-making engine using multiple algorithms"""
    
    def __init__(self):
        self.action_history = []
        self.last_action_time = {}
        self.combat_cooldown = 0
        self.recall_cooldown = 0
        
    def make_optimal_decision(self, game_state: GameState) -> str:
        """
        Main decision-making method using multiple algorithms:
        1. Utility-based scoring
        2. State machine logic
        3. Risk assessment
        4. Opportunity cost analysis
        """
        
        # First, check for emergency situations
        emergency_action = self._check_emergency_conditions(game_state)
        if emergency_action:
            return emergency_action
            
        # Calculate utility scores for all possible actions
        action_utilities = self._calculate_action_utilities(game_state)
        
        # Apply risk assessment
        risk_adjusted_utilities = self._apply_risk_assessment(action_utilities, game_state)
        
        # Consider action cooldowns and history
        final_utilities = self._apply_temporal_constraints(risk_adjusted_utilities, game_state)
        
        # Select best action
        best_action = max(final_utilities, key=final_utilities.get)
        
        # Update history
        self._update_action_history(best_action, game_state)
        
        return best_action
    
    def _check_emergency_conditions(self, game_state: GameState) -> Optional[str]:
        """Check for situations requiring immediate action"""
        
        # Critical health - immediate recall
        if game_state.health < config.HEALTH_CRITICAL_THRESHOLD:
            if not game_state.enemy or self._can_safely_recall(game_state):
                return "recall"
            else:
                return "retreat"  # Can't recall, must retreat
        
        # Enemy very close and we're low health
        if (game_state.enemy and game_state.health < config.MIN_HEALTH_FOR_COMBAT):
            return "retreat"
            
        return None
    
    def _calculate_action_utilities(self, game_state: GameState) -> Dict[str, float]:
        """Calculate base utility scores for each action"""
        utilities = {}
        
        # Recall utility
        utilities['recall'] = self._calculate_recall_utility(game_state)
        
        # Combat utility
        utilities['fight_champion'] = self._calculate_combat_utility(game_state)
        
        # Farming utility
        utilities['farm'] = self._calculate_farming_utility(game_state)
        
        # Movement utility
        utilities['move_to_lane'] = self._calculate_movement_utility(game_state)
        
        # Retreat utility
        utilities['retreat'] = self._calculate_retreat_utility(game_state)
        
        return utilities
    
    def _calculate_recall_utility(self, game_state: GameState) -> float:
        """Calculate utility of recalling"""
        utility = 0
        
        # Health factor
        health_factor = (100 - game_state.health) / 100
        utility += health_factor * config.WEIGHTS['SAFETY'] * 100
        
        # Mana factor (if implemented)
        # mana_factor = (100 - game_state.mana) / 100
        # utility += mana_factor * config.WEIGHTS['SAFETY'] * 50
        
        # Gold efficiency (if we have enough gold to buy items)
        if game_state.gold > 1000:  # Arbitrary threshold
            utility += 200
        
        # Penalty if enemies nearby
        if game_state.enemy:
            utility -= 300
            
        # Cooldown penalty
        if time.time() - self.last_action_time.get('recall', 0) < 30:
            utility -= 500  # Don't recall too frequently
            
        return max(0, utility)
    
    def _calculate_combat_utility(self, game_state: GameState) -> float:
        """Calculate utility of engaging in combat"""
        if not game_state.enemy:
            return 0
            
        utility = 0
        
        # Base combat value
        utility += 400
        
        # Health advantage
        if game_state.health > config.HEALTH_SAFE_THRESHOLD:
            utility += 200
        elif game_state.health < config.MIN_HEALTH_FOR_COMBAT:
            return 0  # Too risky
        
        # Ability availability
        available_abilities = sum(1 for ability in ['Q', 'W', 'E', 'R'] 
                                if game_state.abilities.get(ability, False))
        if available_abilities < config.MIN_ABILITIES_FOR_COMBAT:
            utility -= 200
        else:
            utility += available_abilities * 75
        
        # Level advantage (placeholder - would need enemy level detection)
        # utility += (game_state.level - enemy_level) * 50
        
        return max(0, utility)
    
    def _calculate_farming_utility(self, game_state: GameState) -> float:
        """Calculate utility of farming minions"""
        if not game_state.minions:
            return 0
            
        utility = 0
        
        # Base farming value
        utility += 300
        
        # Number of minions
        utility += len(game_state.minions) * 50
        
        # Health safety for farming
        if game_state.health > 40:
            utility += 100
        
        # Penalty if enemy present (risky farming)
        if game_state.enemy:
            distance_penalty = 200  # Would calculate actual distance in real implementation
            utility -= distance_penalty
        
        # Economic efficiency
        if game_state.gold < 500:  # Need gold for items
            utility += 150
            
        return max(0, utility)
    
    def _calculate_movement_utility(self, game_state: GameState) -> float:
        """Calculate utility of moving to lane"""
        utility = 100  # Base movement value
        
        # Penalty if there are better opportunities
        if game_state.minions:
            utility -= 150
        if game_state.enemy and game_state.health > config.MIN_HEALTH_FOR_COMBAT:
            utility -= 100
            
        return max(0, utility)
    
    def _calculate_retreat_utility(self, game_state: GameState) -> float:
        """Calculate utility of retreating"""
        utility = 0
        
        # High utility if low health and enemy present
        if game_state.enemy and game_state.health < config.MIN_HEALTH_FOR_COMBAT:
            utility += 800
        
        # Moderate utility if outnumbered or outleveled
        # (Would need more game state information)
        
        return utility
    
    def _apply_risk_assessment(self, utilities: Dict[str, float], 
                             game_state: GameState) -> Dict[str, float]:
        """Apply risk assessment to modify utilities"""
        risk_adjusted = utilities.copy()
        
        # Increase safety actions if health is low
        if game_state.health < 30:
            risk_adjusted['recall'] *= 1.5
            risk_adjusted['retreat'] *= 1.3
            risk_adjusted['fight_champion'] *= 0.3
        
        # Reduce risky actions if no escape abilities
        if not game_state.abilities.get('Flash', False):
            risk_adjusted['fight_champion'] *= 0.7
        
        return risk_adjusted
    
    def _apply_temporal_constraints(self, utilities: Dict[str, float], 
                                  game_state: GameState) -> Dict[str, float]:
        """Apply cooldowns and action history constraints"""
        constrained = utilities.copy()
        
        # Prevent action spam
        current_time = time.time()
        for action, last_time in self.last_action_time.items():
            if current_time - last_time < 2.0:  # 2 second cooldown
                constrained[action] *= 0.1
        
        return constrained
    
    def _can_safely_recall(self, game_state: GameState) -> bool:
        """Check if it's safe to recall"""
        if not game_state.enemy:
            return True
        
        # Would implement distance calculation and enemy threat assessment
        return False
    
    def _update_action_history(self, action: str, game_state: GameState):
        """Update action history for future decisions"""
        self.action_history.append((action, game_state.timestamp))
        self.last_action_time[action] = game_state.timestamp
        
        # Keep only recent history
        cutoff_time = game_state.timestamp - 60  # Last minute
        self.action_history = [(a, t) for a, t in self.action_history if t > cutoff_time]
